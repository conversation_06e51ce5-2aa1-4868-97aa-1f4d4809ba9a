<%= form_with(model: [@recipe, variant], local: true) do |form| %>
  <% if variant.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(variant.errors.count, "error") %> prohibited this variant from being saved:</h2>
      <ul>
        <% variant.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-section">
    <h3>Variant Details</h3>

    <div class="field">
      <%= form.label :dietary_requirement_ids, "Dietary requirements" %>
      <div class="checkbox-group">
        <% DietaryRequirement.active_alphabetical.each do |req| %>
          <label class="checkbox-label">
            <%= check_box_tag "recipe_variant[dietary_requirement_ids][]", req.id, variant.dietary_requirements.include?(req), id: "recipe_variant_dietary_requirement_ids_#{req.id}" %>
            <%= req.name %>
          </label>
        <% end %>
      </div>
    </div>

    <div class="field">
      <%= form.label :name, "Custom Name (optional)" %>
      <%= form.text_field :name, placeholder: "e.g., 'Crispy Tofu Version'" %>
      <small>Leave blank to use default name based on dietary requirement</small>
    </div>
  </div>

  <div class="form-section">
    <h3>Cooking Instructions</h3>

    <div class="field">
      <%= form.label :instructions, "Cooking Instructions" %>
      <%= form.rich_text_area :instructions, placeholder: "Detailed cooking instructions for this variant..." %>
    </div>

    <div class="field">
      <%= form.label :working_minutes, "Prep Time (minutes)" %>
      <%= form.number_field :working_minutes, min: 0 %>
    </div>

    <div class="field">
      <%= form.label :total_minutes, "Total Time (minutes)" %>
      <%= form.number_field :total_minutes, min: 0 %>
    </div>

    <div class="field">
      <%= form.label :notes %>
      <%= form.text_area :notes, rows: 3, placeholder: "Additional notes or tips for this variant..." %>
    </div>
  </div>

  <div class="actions">
    <%= form.submit class: "btn btn-primary" %>
    <%= link_to "Cancel", recipe_path(@recipe), class: "btn btn-secondary" %>
  </div>
<% end %>
