<div class="header">
  <h1>Admin Management</h1>
</div>

<div class="admin-content">
  <!-- Cuisines Section -->
  <div class="enum-section">
    <h2>Cuisines (<%= @cuisines.count %>)</h2>

    <div class="add-form">
      <%= form_with url: admin_create_enum_path("Cuisine"), local: true do |form| %>
        <%= form.text_field "item[name]", placeholder: "Cuisine name", required: true %>
        <%= form.text_field "item[description]", placeholder: "Description" %>
        <%= form.submit "Add Cuisine", class: "btn btn-primary" %>
      <% end %>
    </div>

    <div class="enum-list">
      <% @cuisines.each do |cuisine| %>
        <div class="enum-item <%= 'inactive' unless cuisine.active %>">
          <span class="name"><%= cuisine.name %></span>
          <span class="description"><%= cuisine.description %></span>
          <span class="status <%= cuisine.active? ? 'active' : 'inactive' %>">
            <%= cuisine.active? ? 'Active' : 'Inactive' %>
          </span>
          <div class="enum-actions">
            <%= button_to cuisine.active? ? 'Deactivate' : 'Activate',
                          admin_toggle_enum_path("Cuisine", cuisine.id),
                          method: :post,
                          class: "btn btn-sm #{cuisine.active? ? 'btn-warning' : 'btn-success'}" %>
            <%= button_to 'Delete',
                          admin_destroy_enum_path("Cuisine", cuisine.id),
                          method: :delete,
                          data: { turbo_confirm: "Are you sure? This will permanently delete this cuisine." },
                          class: "btn btn-sm btn-danger" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Proteins Section -->
  <div class="enum-section">
    <h2>Proteins (<%= @proteins.count %>)</h2>

    <div class="add-form">
      <%= form_with url: admin_create_enum_path("Protein"), local: true do |form| %>
        <%= form.text_field "item[name]", placeholder: "Protein name", required: true %>
        <%= form.text_field "item[description]", placeholder: "Description" %>
        <%= form.submit "Add Protein", class: "btn btn-primary" %>
      <% end %>
    </div>

    <div class="enum-list">
      <% @proteins.each do |protein| %>
        <div class="enum-item <%= 'inactive' unless protein.active %>">
          <span class="name"><%= protein.name %></span>
          <span class="description"><%= protein.description %></span>
          <span class="status <%= protein.active? ? 'active' : 'inactive' %>">
            <%= protein.active? ? 'Active' : 'Inactive' %>
          </span>
          <div class="enum-actions">
            <%= button_to protein.active? ? 'Deactivate' : 'Activate',
                          admin_toggle_enum_path("Protein", protein.id),
                          method: :post,
                          class: "btn btn-sm #{protein.active? ? 'btn-warning' : 'btn-success'}" %>
            <%= button_to 'Delete',
                          admin_destroy_enum_path("Protein", protein.id),
                          method: :delete,
                          data: { turbo_confirm: "Are you sure? This will permanently delete this protein." },
                          class: "btn btn-sm btn-danger" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Tags Section -->
  <div class="enum-section">
    <h2>Tags (<%= @tags.count %>)</h2>

    <div class="add-form">
      <%= form_with url: admin_create_enum_path("Tag"), local: true do |form| %>
        <%= form.text_field "item[name]", placeholder: "Tag name", required: true %>
        <%= form.text_field "item[description]", placeholder: "Description" %>
        <%= form.submit "Add Tag", class: "btn btn-primary" %>
      <% end %>
    </div>

    <div class="enum-list">
      <% @tags.each do |tag| %>
        <div class="enum-item <%= 'inactive' unless tag.active %>">
          <span class="name"><%= tag.name %></span>
          <span class="description"><%= tag.description %></span>
          <span class="status <%= tag.active? ? 'active' : 'inactive' %>">
            <%= tag.active? ? 'Active' : 'Inactive' %>
          </span>
          <div class="enum-actions">
            <%= button_to tag.active? ? 'Deactivate' : 'Activate',
                          admin_toggle_enum_path("Tag", tag.id),
                          method: :post,
                          class: "btn btn-sm #{tag.active? ? 'btn-warning' : 'btn-success'}" %>
            <%= button_to 'Delete',
                          admin_destroy_enum_path("Tag", tag.id),
                          method: :delete,
                          data: { turbo_confirm: "Are you sure? This will permanently delete this tag." },
                          class: "btn btn-sm btn-danger" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Dietary Requirements Section -->
  <div class="enum-section">
    <h2>Dietary Requirements (<%= @dietary_requirements.count %>)</h2>

    <div class="add-form">
      <%= form_with url: admin_create_enum_path("DietaryRequirement"), local: true do |form| %>
        <%= form.text_field "item[name]", placeholder: "Dietary requirement name", required: true %>
        <%= form.text_field "item[description]", placeholder: "Description" %>
        <%= form.submit "Add Dietary Requirement", class: "btn btn-primary" %>
      <% end %>
    </div>

    <div class="enum-list">
      <% @dietary_requirements.each do |req| %>
        <div class="enum-item <%= 'inactive' unless req.active %>">
          <span class="name"><%= req.name %></span>
          <span class="description"><%= req.description %></span>
          <span class="status <%= req.active? ? 'active' : 'inactive' %>">
            <%= req.active? ? 'Active' : 'Inactive' %>
          </span>
          <div class="enum-actions">
            <%= button_to req.active? ? 'Deactivate' : 'Activate',
                          admin_toggle_enum_path("DietaryRequirement", req.id),
                          method: :post,
                          class: "btn btn-sm #{req.active? ? 'btn-warning' : 'btn-success'}" %>
            <%= button_to 'Delete',
                          admin_destroy_enum_path("DietaryRequirement", req.id),
                          method: :delete,
                          data: { turbo_confirm: "Are you sure? This will permanently delete this dietary requirement." },
                          class: "btn btn-sm btn-danger" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
