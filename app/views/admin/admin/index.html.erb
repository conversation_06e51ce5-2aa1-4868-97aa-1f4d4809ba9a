<div class="centered-container">
  <div class="header">
    <h1>Admin Management</h1>
    <p>Customize global app options and settings</p>
  </div>

  <div class="admin-content">

    <!-- Ingredients Section -->
    <div class="admin-section">
      <div class="section-header" onclick="toggleSection('ingredients')">
        <h2>Ingredients (<%= @ingredients.count %>)</h2>
        <span class="toggle-icon" id="ingredients-icon">▼</span>
      </div>
      <div class="section-content" id="ingredients-content">
        <div class="add-form">
          <%= link_to "Add New Ingredient", new_admin_ingredient_path, class: "btn btn-primary" %>
        </div>

        <div class="ingredients-list">
          <% @ingredients.each do |ingredient| %>
            <div class="ingredient-card">
              <div class="card-content">
                <h3><%= ingredient.name %></h3>
                <% if ingredient.description.present? %>
                  <p class="description"><%= ingredient.description %></p>
                <% end %>
              </div>
              <div class="card-actions">
                <%= link_to "Edit", edit_admin_ingredient_path(ingredient), class: "btn btn-sm btn-secondary" %>
                <%= link_to "Delete", admin_ingredient_path(ingredient),
                            data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
                            class: "btn btn-sm btn-danger" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    
    <!-- Cuisines Section -->
    <div class="admin-section">
      <div class="section-header" onclick="toggleSection('cuisines')">
        <h2>Cuisines (<%= @cuisines.count %>)</h2>
        <span class="toggle-icon" id="cuisines-icon">▼</span>
      </div>
      <div class="section-content" id="cuisines-content">
        <div class="add-form">
          <%= form_with url: admin_create_enum_path("Cuisine"), local: true do |form| %>
            <%= form.text_field "item[name]", placeholder: "Cuisine name", required: true %>
            <%= form.text_field "item[description]", placeholder: "Description" %>
            <%= form.submit "Add Cuisine", class: "btn btn-primary" %>
          <% end %>
        </div>

        <div class="enum-list">
          <% @cuisines.each do |cuisine| %>
            <div class="enum-item <%= 'inactive' unless cuisine.active %>">
              <span class="name"><%= cuisine.name %></span>
              <span class="description"><%= cuisine.description %></span>
              <span class="status <%= cuisine.active? ? 'active' : 'inactive' %>">
                <%= cuisine.active? ? 'Active' : 'Inactive' %>
              </span>
              <div class="enum-actions">
                <%= button_to cuisine.active? ? 'Deactivate' : 'Activate',
                              admin_toggle_enum_path("Cuisine", cuisine.id),
                              method: :post,
                              class: "btn btn-sm #{cuisine.active? ? 'btn-warning' : 'btn-success'}" %>
                <%= button_to 'Delete',
                              admin_destroy_enum_path("Cuisine", cuisine.id),
                              method: :delete,
                              data: { turbo_confirm: "Are you sure?" },
                              class: "btn btn-sm btn-danger" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Proteins Section -->
    <div class="admin-section">
      <div class="section-header" onclick="toggleSection('proteins')">
        <h2>Proteins (<%= @proteins.count %>)</h2>
        <span class="toggle-icon" id="proteins-icon">▼</span>
      </div>
      <div class="section-content" id="proteins-content">
        <div class="add-form">
          <%= form_with url: admin_create_enum_path("Protein"), local: true do |form| %>
            <%= form.text_field "item[name]", placeholder: "Protein name", required: true %>
            <%= form.text_field "item[description]", placeholder: "Description" %>
            <%= form.submit "Add Protein", class: "btn btn-primary" %>
          <% end %>
        </div>

        <div class="enum-list">
          <% @proteins.each do |protein| %>
            <div class="enum-item <%= 'inactive' unless protein.active %>">
              <span class="name"><%= protein.name %></span>
              <span class="description"><%= protein.description %></span>
              <span class="status <%= protein.active? ? 'active' : 'inactive' %>">
                <%= protein.active? ? 'Active' : 'Inactive' %>
              </span>
              <div class="enum-actions">
                <%= button_to protein.active? ? 'Deactivate' : 'Activate',
                              admin_toggle_enum_path("Protein", protein.id),
                              method: :post,
                              class: "btn btn-sm #{protein.active? ? 'btn-warning' : 'btn-success'}" %>
                <%= button_to 'Delete',
                              admin_destroy_enum_path("Protein", protein.id),
                              method: :delete,
                              data: { turbo_confirm: "Are you sure?" },
                              class: "btn btn-sm btn-danger" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Tags Section -->
    <div class="admin-section">
      <div class="section-header" onclick="toggleSection('tags')">
        <h2>Tags (<%= @tags.count %>)</h2>
        <span class="toggle-icon" id="tags-icon">▼</span>
      </div>
      <div class="section-content" id="tags-content">
        <div class="add-form">
          <%= form_with url: admin_create_enum_path("Tag"), local: true do |form| %>
            <%= form.text_field "item[name]", placeholder: "Tag name", required: true %>
            <%= form.text_field "item[description]", placeholder: "Description" %>
            <%= form.submit "Add Tag", class: "btn btn-primary" %>
          <% end %>
        </div>

        <div class="enum-list">
          <% @tags.each do |tag| %>
            <div class="enum-item <%= 'inactive' unless tag.active %>">
              <span class="name"><%= tag.name %></span>
              <span class="description"><%= tag.description %></span>
              <span class="status <%= tag.active? ? 'active' : 'inactive' %>">
                <%= tag.active? ? 'Active' : 'Inactive' %>
              </span>
              <div class="enum-actions">
                <%= button_to tag.active? ? 'Deactivate' : 'Activate',
                              admin_toggle_enum_path("Tag", tag.id),
                              method: :post,
                              class: "btn btn-sm #{tag.active? ? 'btn-warning' : 'btn-success'}" %>
                <%= button_to 'Delete',
                              admin_destroy_enum_path("Tag", tag.id),
                              method: :delete,
                              data: { turbo_confirm: "Are you sure?" },
                              class: "btn btn-sm btn-danger" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Dietary Requirements Section -->
    <div class="admin-section">
      <div class="section-header" onclick="toggleSection('dietary_requirements')">
        <h2>Dietary Requirements (<%= @dietary_requirements.count %>)</h2>
        <span class="toggle-icon" id="dietary_requirements-icon">▼</span>
      </div>
      <div class="section-content" id="dietary_requirements-content">
        <div class="add-form">
          <%= form_with url: admin_create_enum_path("DietaryRequirement"), local: true do |form| %>
            <%= form.text_field "item[name]", placeholder: "Dietary requirement name", required: true %>
            <%= form.text_field "item[description]", placeholder: "Description" %>
            <%= form.submit "Add Dietary Requirement", class: "btn btn-primary" %>
          <% end %>
        </div>

        <div class="enum-list">
          <% @dietary_requirements.each do |req| %>
            <div class="enum-item <%= 'inactive' unless req.active %>">
              <span class="name"><%= req.name %></span>
              <span class="description"><%= req.description %></span>
              <span class="status <%= req.active? ? 'active' : 'inactive' %>">
                <%= req.active? ? 'Active' : 'Inactive' %>
              </span>
              <div class="enum-actions">
                <%= button_to req.active? ? 'Deactivate' : 'Activate',
                              admin_toggle_enum_path("DietaryRequirement", req.id),
                              method: :post,
                              class: "btn btn-sm #{req.active? ? 'btn-warning' : 'btn-success'}" %>
                <%= button_to 'Delete',
                              admin_destroy_enum_path("DietaryRequirement", req.id),
                              method: :delete,
                              data: { turbo_confirm: "Are you sure?" },
                              class: "btn btn-sm btn-danger" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Units Section -->
    <div class="admin-section">
      <div class="section-header" onclick="toggleSection('units')">
        <h2>Units (<%= @units.count %>)</h2>
        <span class="toggle-icon" id="units-icon">▼</span>
      </div>
      <div class="section-content" id="units-content">
        <div class="add-form">
          <%= link_to "Add New Unit", new_admin_unit_path, class: "btn btn-primary" %>
        </div>

        <div class="units-content">
          <% Unit.unit_types.each do |type, _| %>
            <% units_of_type = @units.select { |u| u.unit_type == type } %>
            <% if units_of_type.any? %>
              <div class="unit-subsection">
                <h3><%= type.humanize %> Units (<%= units_of_type.count %>)</h3>
                <div class="units-list">
                  <% units_of_type.each do |unit| %>
                    <div class="unit-card">
                      <div class="card-content">
                        <h4><%= unit.name %></h4>
                        <div class="unit-details">
                          <span class="abbreviation"><strong>Abbreviation:</strong> <%= unit.abbreviation %></span>
                          <span class="measure"><strong>System:</strong> <%= unit.unit_system.humanize %></span>
                          <% if unit.universal? %>
                            <span class="universal-badge">Universal</span>
                          <% end %>
                        </div>
                      </div>
                      <div class="card-actions">
                        <%= link_to "Edit", edit_admin_unit_path(unit), class: "btn btn-sm btn-secondary" %>
                        <%= link_to "Delete", admin_unit_path(unit),
                                    data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
                                    class: "btn btn-sm btn-danger" %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <script>
  function toggleSection(sectionId) {
    const content = document.getElementById(sectionId + '-content');
    const icon = document.getElementById(sectionId + '-icon');

    if (content.style.display === 'none') {
      content.style.display = 'block';
      icon.textContent = '▼';
    } else {
      content.style.display = 'none';
      icon.textContent = '▶';
    }
  }

  // Initialize all sections as collapsed
  document.addEventListener('DOMContentLoaded', function() {
    const sections = ['cuisines', 'proteins', 'tags', 'dietary_requirements', 'ingredients', 'units'];
    sections.forEach(function(sectionId) {
      toggleSection(sectionId);
    });
  });
  </script>
</div>
