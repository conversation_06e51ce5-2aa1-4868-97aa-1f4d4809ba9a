<div class="centered-container">
  <div class="header">
    <h1>Edit <%= @type %></h1>
    <p>Update the details for this <%= @type.downcase %></p>
  </div>

  <div class="form-section">
    <%= form_with model: @item, url: admin_update_enum_path(@type, @item), method: :patch, local: true do |form| %>
      <% if @item.errors.any? %>
        <div id="error_explanation">
          <h2><%= pluralize(@item.errors.count, "error") %> prohibited this <%= @type.downcase %> from being saved:</h2>
          <ul>
            <% @item.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div class="field">
        <%= form.label :name %>
        <%= form.text_field :name, required: true, maxlength: 50 %>
      </div>

      <div class="field">
        <%= form.label :description %>
        <%= form.text_area :description, rows: 3 %>
      </div>

      <div class="actions">
        <%= form.submit "Update #{@type}", class: "btn btn-primary" %>
        <%= link_to "Cancel", admin_root_path, class: "btn btn-secondary" %>
      </div>
    <% end %>
  </div>
</div>
