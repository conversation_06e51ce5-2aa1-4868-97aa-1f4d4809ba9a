<h1>Edit Comment</h1>

<%= form_with(model: [@recipe, @comment], local: true) do |form| %>
  <% if @comment.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(@comment.errors.count, "error") %> prohibited this comment from being saved:</h2>
      <ul>
        <% @comment.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="field">
    <%= form.label :comment %>
    <%= form.rich_text_area :comment, required: true %>
  </div>

  <div class="actions">
    <%= form.submit "Update Comment" %>
    <%= link_to "Cancel", @recipe, class: "btn btn-secondary" %>
  </div>
<% end %>
