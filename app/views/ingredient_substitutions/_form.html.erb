<%= form_with(model: [@recipe_ingredient, substitution], local: true) do |form| %>
  <% if substitution.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(substitution.errors.count, "error") %> prohibited this substitution from being saved:</h2>
      <ul>
        <% substitution.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-section">
    <h3>Original Ingredient</h3>
    <p><strong><%= @recipe_ingredient.ingredient.name %></strong> - <%= @recipe_ingredient.amount %> <%= @recipe_ingredient.unit.name %></p>
    <% if @recipe_ingredient.preparation.present? %>
      <p>Preparation: <%= @recipe_ingredient.preparation.humanize %></p>
    <% end %>
  </div>

  <div class="form-section">
    <h3>Substitution Details</h3>
    
    <div class="field">
      <%= form.label :dietary_requirement %>
      <%= form.select :dietary_requirement, 
                      options_for_select(IngredientSubstitution.dietary_requirements.map { |k, v| [k.humanize, k] }, substitution.dietary_requirement),
                      { prompt: "Select dietary requirement" },
                      { required: true } %>
    </div>

    <div class="field">
      <%= form.label :substitute_ingredient_id, "Substitute ingredient" %>
      <%= form.collection_select :substitute_ingredient_id, 
                                 Ingredient.order(:name), 
                                 :id, :name, 
                                 { prompt: "Select substitute ingredient" },
                                 { required: true } %>
    </div>

    <div class="field">
      <%= form.label :substitute_amount, "Amount" %>
      <%= form.number_field :substitute_amount, step: 0.01, min: 0.01, required: true %>
    </div>

    <div class="field">
      <%= form.label :substitute_unit_id, "Unit" %>
      <%= form.collection_select :substitute_unit_id, 
                                 Unit.order(:unit_type, :name), 
                                 :id, :name, 
                                 { prompt: "Select unit" },
                                 { required: true } %>
    </div>

    <div class="field">
      <%= form.label :substitute_preparation, "Preparation method" %>
      <%= form.select :substitute_preparation, 
                      options_for_select(IngredientSubstitution.substitute_preparations.map { |k, v| [k.humanize, k] }, substitution.substitute_preparation),
                      { prompt: "Select preparation method" } %>
    </div>

    <div class="field">
      <%= form.label :substitute_notes, "Notes" %>
      <%= form.text_area :substitute_notes, rows: 3 %>
    </div>
  </div>

  <div class="actions">
    <%= form.submit class: "btn btn-primary" %>
    <%= link_to "Cancel", @recipe, class: "btn btn-secondary" %>
  </div>
<% end %>
