<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Biteplan" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body>
    <nav>
      <div class="nav-inner">
        <%= link_to root_path do %>
          <%= image_tag "logo.svg", alt: "Biteplan", class: "logo" %>
        <% end %>
        <div class="nav-links">
          <%= link_to "Log in", new_session_path unless authenticated? || request.path == new_session_path %>
          <% if admin? %>
            <%= link_to "New Recipe", new_recipe_path %>
            <%= link_to "Admin", admin_root_path %>
          <% end %>
          <% if authenticated? %>
            <%= link_to "Preferences", edit_user_path %>
            <%= link_to "Log out", session_path, method: :delete, data: { turbo_method: :delete } %>
          <% end %>
        </div>
      </div>
    </nav>
    <main>
      <% if notice %>
        <div class="notice"><%= notice %></div>
      <% end %>
      <% if alert %>
        <div class="alert"><%= alert %></div>
      <% end %>
      <%= yield %>
    </main>
  </body>
</html>
