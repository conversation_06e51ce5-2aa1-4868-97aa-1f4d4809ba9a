<div class="header">
  <h1>User Preferences</h1>
  <p>Customize your recipe viewing experience</p>
</div>

<div class="form-section">
  <%= form_with model: @user, local: true do |form| %>
    <% if @user.errors.any? %>
      <div id="error_explanation">
        <h2><%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h2>
        <ul>
          <% @user.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="field">
      <%= form.label :unit_preference, "Preferred Unit System" %>
      <%= form.select :unit_preference,
                      options_for_select([
                        ['Metric (grams, milliliters, Celsius)', 'metric'],
                        ['Imperial (ounces, fluid ounces, Fahrenheit)', 'imperial']
                      ], @user.unit_preference),
                      { prompt: 'Select your preferred unit system' },
                      { class: 'form-control' } %>
      <small class="form-text text-muted">
        Recipes will be displayed in your preferred unit system when possible.
        Universal units like teaspoons and tablespoons remain unchanged.
      </small>
    </div>

    <div class="actions">
      <%= form.submit "Update Preferences", class: "btn btn-primary" %>
      <%= link_to "Back to Recipes", root_path, class: "btn btn-secondary" %>
    </div>
  <% end %>
</div>
