<h1>Ingredients</h1>

<%= link_to "New Ingredient", new_ingredient_path, class: "btn btn-primary" %>

<div class="ingredients-list">
  <% @ingredients.each do |ingredient| %>
    <div class="ingredient-card">
      <h3><%= link_to ingredient.name, ingredient %></h3>
      <% if ingredient.description.present? %>
        <p><%= ingredient.description %></p>
      <% end %>
      <div class="actions">
        <%= link_to "Edit", edit_ingredient_path(ingredient) %>
        <%= link_to "Delete", ingredient, method: :delete,
                    confirm: "Are you sure?",
                    class: "text-danger" %>
      </div>
    </div>
  <% end %>
</div>

<% if @ingredients.empty? %>
  <p>No ingredients found. <%= link_to "Create one", new_ingredient_path %>!</p>
<% end %>
