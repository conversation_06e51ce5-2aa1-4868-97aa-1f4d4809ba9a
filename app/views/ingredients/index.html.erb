<div class="centered-container">
  <div class="header">
    <h1>Ingredients</h1>
    <p>Manage your ingredient library</p>
  </div>

  <% if admin? %>
    <div class="add-form">
      <%= link_to "Add New Ingredient", new_ingredient_path, class: "btn btn-primary" %>
    </div>
  <% end %>

  <% if @ingredients.any? %>
    <div class="ingredients-list">
      <% @ingredients.each do |ingredient| %>
        <div class="ingredient-card">
          <div class="card-content">
            <h3><%= ingredient.name %></h3>
            <% if ingredient.description.present? %>
              <p class="description"><%= ingredient.description %></p>
            <% end %>
          </div>
          <% if admin? %>
            <div class="card-actions">
              <%= link_to "Edit", edit_ingredient_path(ingredient), class: "btn btn-sm btn-secondary" %>
              <%= link_to "Delete", ingredient,
                          data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
                          class: "btn btn-sm btn-danger" %>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="empty-state">
      <h3>No ingredients yet</h3>
      <p>Start building your ingredient library</p>
      <% if admin? %>
        <%= link_to "Add First Ingredient", new_ingredient_path, class: "btn btn-primary" %>
      <% end %>
    </div>
  <% end %>
</div>
