<h1><%= @ingredient.name %></h1>

<% if @ingredient.description.present? %>
  <p><strong>Description:</strong></p>
  <p><%= @ingredient.description %></p>
<% end %>

<div class="actions">
  <%= link_to 'Edit', edit_ingredient_path(@ingredient), class: "btn btn-primary" %>
  <%= link_to 'Delete', @ingredient,
              data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
              class: "btn btn-danger" %>
  <%= link_to 'Back', ingredients_path, class: "btn btn-secondary" %>
</div>

<% if @ingredient.recipes.any? %>
  <h3>Used in Recipes:</h3>
  <ul>
    <% @ingredient.recipes.each do |recipe| %>
      <li><%= link_to recipe.title, recipe %></li>
    <% end %>
  </ul>
<% end %>
