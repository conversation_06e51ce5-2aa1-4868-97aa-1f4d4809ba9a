<div class="centered-container">
  <div class="header">
    <h1><%= @ingredient.name %></h1>
    <% if @ingredient.description.present? %>
      <p><%= @ingredient.description %></p>
    <% end %>
  </div>

  <% if admin? %>
    <div class="form-section">
      <h3>Actions</h3>
      <div class="actions">
        <%= link_to 'Edit', edit_ingredient_path(@ingredient), class: "btn btn-primary" %>
        <%= link_to 'Delete', @ingredient,
                    data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
                    class: "btn btn-danger" %>
        <%= link_to 'Back to Ingredients', ingredients_path, class: "btn btn-secondary" %>
      </div>
    </div>
  <% else %>
    <div class="actions">
      <%= link_to 'Back to Ingredients', ingredients_path, class: "btn btn-secondary" %>
    </div>
  <% end %>

  <% if @ingredient.recipes.any? %>
    <div class="form-section">
      <h3>Used in Recipes (<%= @ingredient.recipes.count %>)</h3>
      <div class="recipe-list">
        <% @ingredient.recipes.each do |recipe| %>
          <div class="recipe-link">
            <%= link_to recipe.title, recipe, class: "btn btn-outline" %>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>
</div>
