<div class="centered-container">
  <div class="form-section">
    <%= form_with(model: ingredient, local: true) do |form| %>
      <% if ingredient.errors.any? %>
        <div id="error_explanation">
          <h2><%= pluralize(ingredient.errors.count, "error") %> prohibited this ingredient from being saved:</h2>
          <ul>
            <% ingredient.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div class="field">
        <%= form.label :name %>
        <%= form.text_field :name, required: true, maxlength: 50 %>
      </div>

      <div class="field">
        <%= form.label :description %>
        <%= form.text_area :description, rows: 3 %>
      </div>

      <div class="actions">
        <%= form.submit class: "btn btn-primary" %>
        <%= link_to "Cancel", ingredients_path, class: "btn btn-secondary" %>
      </div>
    <% end %>
  </div>
</div>
