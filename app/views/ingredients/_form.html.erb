<%= form_with(model: ingredient, local: true) do |form| %>
  <% if ingredient.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(ingredient.errors.count, "error") %> prohibited this ingredient from being saved:</h2>
      <ul>
        <% ingredient.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="field">
    <%= form.label :name %>
    <%= form.text_field :name, required: true, maxlength: 50 %>
  </div>

  <div class="field">
    <%= form.label :description %>
    <%= form.text_area :description, rows: 3 %>
  </div>

  <div class="actions">
    <%= form.submit %>
    <%= link_to "Cancel", ingredient_path(ingredient), class: "btn btn-secondary" if ingredient.persisted? %>
    <%= link_to "Cancel", ingredients_path, class: "btn btn-secondary" unless ingredient.persisted? %>
  </div>
<% end %>
