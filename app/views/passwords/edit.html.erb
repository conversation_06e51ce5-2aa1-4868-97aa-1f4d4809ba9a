<div class="auth-container">
  <div class="auth-header">
    <h1>Update your password</h1>
    <p>Choose a strong password for your account</p>
  </div>

  <% if flash[:alert] %>
    <div class="alert"><%= flash[:alert] %></div>
  <% end %>
  <% if flash[:notice] %>
    <div class="notice"><%= flash[:notice] %></div>
  <% end %>

  <div class="form-section">
    <%= form_with url: password_path(params[:token]), method: :put, local: true do |form| %>
      <div class="field">
        <%= form.label :password, "New Password" %>
        <%= form.password_field :password, required: true, autocomplete: "new-password", placeholder: "Enter new password", maxlength: 72 %>
      </div>

      <div class="field">
        <%= form.label :password_confirmation, "Confirm Password" %>
        <%= form.password_field :password_confirmation, required: true, autocomplete: "new-password", placeholder: "Repeat new password", maxlength: 72 %>
      </div>

      <div class="actions">
        <%= form.submit "Save Password", class: "btn btn-primary" %>
      </div>
    <% end %>

    <div class="auth-links">
      <%= link_to "Back to Sign In", new_session_path, class: "btn btn-outline" %>
    </div>
  </div>
</div>
