<div class="auth-container">
  <div class="auth-header">
    <h1>Sign In</h1>
    <p>Welcome back to Biteplan</p>
  </div>

  <% if flash[:alert] %>
    <div class="alert"><%= flash[:alert] %></div>
  <% end %>
  <% if flash[:notice] %>
    <div class="notice"><%= flash[:notice] %></div>
  <% end %>

  <div class="form-section">
    <%= form_with url: session_path, local: true do |form| %>
      <div class="field">
        <%= form.label :email_address, "Email Address" %>
        <%= form.email_field :email_address, required: true, autofocus: true, autocomplete: "username", placeholder: "Enter your email address", value: params[:email_address] %>
      </div>

      <div class="field">
        <%= form.label :password, "Password" %>
        <%= form.password_field :password, required: true, autocomplete: "current-password", placeholder: "Enter your password", maxlength: 72 %>
      </div>

      <div class="actions">
        <%= form.submit "Sign in", class: "btn btn-primary" %>
      </div>
    <% end %>

    <div class="auth-links">
      <%= link_to "Forgot password?", new_password_path, class: "btn btn-outline" %>
    </div>
  </div>
</div>
