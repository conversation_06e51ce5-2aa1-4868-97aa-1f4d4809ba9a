<%= form_with(model: [@recipe, recipe_ingredient], local: true) do |form| %>
  <% if recipe_ingredient.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(recipe_ingredient.errors.count, "error") %> prohibited this ingredient from being saved:</h2>
      <ul>
        <% recipe_ingredient.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="field">
    <%= form.label :ingredient_id, "Ingredient" %>
    <%= form.collection_select :ingredient_id, 
                               Ingredient.order(:name), 
                               :id, :name, 
                               { prompt: "Select an ingredient" },
                               { required: true } %>
  </div>

  <div class="field">
    <%= form.label :amount %>
    <%= form.number_field :amount, step: 0.01, min: 0.01, required: true %>
  </div>

  <div class="field">
    <%= form.label :unit_id, "Unit" %>
    <%= form.collection_select :unit_id, 
                               Unit.order(:unit_type, :name), 
                               :id, :name, 
                               { prompt: "Select a unit" },
                               { required: true } %>
  </div>

  <div class="field">
    <%= form.label :preparation %>
    <%= form.select :preparation, 
                    options_for_select(RecipeIngredient.preparations.map { |k, v| [k.humanize, k] }, recipe_ingredient.preparation),
                    { prompt: "Select preparation method" } %>
  </div>

  <div class="field">
    <%= form.label :notes %>
    <%= form.text_area :notes, rows: 2 %>
  </div>

  <div class="field">
    <%= form.label :display_order %>
    <%= form.number_field :display_order, min: 1, required: true %>
  </div>

  <div class="actions">
    <%= form.submit %>
    <%= link_to "Cancel", @recipe, class: "btn btn-secondary" %>
  </div>
<% end %>
