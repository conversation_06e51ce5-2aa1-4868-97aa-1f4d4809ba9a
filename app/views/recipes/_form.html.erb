<div class="centered-container">
  <%= form_with(model: recipe, local: true) do |form| %>
    <% if recipe.errors.any? %>
      <div id="error_explanation">
        <h2><%= pluralize(recipe.errors.count, "error") %> prohibited this recipe from being saved:</h2>
        <ul>
          <% recipe.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

  <div class="form-section">
    <h3>Basic Information</h3>

    <div class="field">
      <%= form.label :title %>
      <%= form.text_field :title, required: true, maxlength: 50 %>
    </div>

    <div class="field">
      <%= form.label :sub_title %>
      <%= form.text_field :sub_title, maxlength: 80 %>
    </div>

    <div class="field">
      <%= form.label :description %>
      <%= form.rich_text_area :description %>
    </div>

    <div class="field">
      <%= form.label :instructions, "Cooking Instructions" %>
      <%= form.rich_text_area :instructions, placeholder: "Step-by-step cooking instructions..." %>
    </div>

    <div class="field">
      <%= form.label :image %>
      <%= form.file_field :image, accept: "image/*" %>
      <% if recipe.image.attached? %>
        <p>Current image: <%= recipe.image.filename %></p>
      <% end %>
    </div>
  </div>

  <div class="form-section">
    <h3>Recipe Details</h3>

    <div class="field">
      <%= form.label :cuisine %>
      <%= form.collection_select :cuisine_id,
                                 Cuisine.active_alphabetical,
                                 :id, :name,
                                 { prompt: "Select cuisine", include_blank: "None" } %>
    </div>

    <div class="field">
      <%= form.label :protein %>
      <%= form.collection_select :protein_id,
                                 Protein.active_alphabetical,
                                 :id, :name,
                                 { prompt: "Select protein type", include_blank: "None" } %>
    </div>

    <div class="field">
      <%= form.label :suggested_serves %>
      <%= form.number_field :suggested_serves, min: 1, required: true %>
    </div>
  </div>

  <div class="form-section">
    <h3>Timing</h3>

    <div class="field">
      <%= form.label :working_minutes, "Prep time (minutes)" %>
      <%= form.number_field :working_minutes, min: 0 %>
    </div>

    <div class="field">
      <%= form.label :total_minutes, "Total time (minutes)" %>
      <%= form.number_field :total_minutes, min: 0 %>
    </div>
  </div>

  <div class="form-section">
    <h3>Tags</h3>

    <div class="field">
      <%= form.label :tag_ids, "Recipe tags" %>
      <div class="checkbox-controls">
        <button type="button" onclick="clearCheckboxes('recipe_tag_ids')" class="btn btn-sm btn-secondary">Clear All</button>
      </div>
      <%= hidden_field_tag "recipe[tag_ids][]", "" %>
      <div class="checkbox-group">
        <% Tag.active_alphabetical.each do |tag| %>
          <label class="checkbox-label">
            <%= check_box_tag "recipe[tag_ids][]", tag.id, recipe.tags.include?(tag), id: "recipe_tag_ids_#{tag.id}" %>
            <%= tag.name %>
          </label>
        <% end %>
      </div>
    </div>
  </div>

  <div class="form-section">
    <h3>Dietary Requirements</h3>

    <div class="field">
      <%= form.label :dietary_requirement_ids, "Dietary requirements" %>
      <div class="checkbox-controls">
        <button type="button" onclick="clearCheckboxes('recipe_dietary_requirement_ids')" class="btn btn-sm btn-secondary">Clear All</button>
      </div>
      <%= hidden_field_tag "recipe[dietary_requirement_ids][]", "" %>
      <div class="checkbox-group">
        <% DietaryRequirement.active_alphabetical.each do |req| %>
          <label class="checkbox-label">
            <%= check_box_tag "recipe[dietary_requirement_ids][]", req.id, recipe.dietary_requirements.include?(req), id: "recipe_dietary_requirement_ids_#{req.id}" %>
            <%= req.name %>
          </label>
        <% end %>
      </div>
    </div>
  </div>

  <div class="actions">
    <%= form.submit class: "btn btn-primary" %>
    <%= link_to "Cancel", recipe_path(recipe), class: "btn btn-secondary" if recipe.persisted? %>
    <%= link_to "Cancel", recipes_path, class: "btn btn-secondary" unless recipe.persisted? %>
  </div>
  <% end %>

  <script>
  function clearCheckboxes(prefix) {
    // Find all checkboxes that start with the given prefix
    const checkboxes = document.querySelectorAll(`input[type="checkbox"][id^="${prefix}"]`);
    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
    });
  }
  </script>
</div>
