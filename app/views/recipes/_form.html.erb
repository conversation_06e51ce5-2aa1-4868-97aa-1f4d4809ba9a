<%= form_with(model: recipe, local: true) do |form| %>
  <% if recipe.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(recipe.errors.count, "error") %> prohibited this recipe from being saved:</h2>
      <ul>
        <% recipe.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-section">
    <h3>Basic Information</h3>

    <div class="field">
      <%= form.label :title %>
      <%= form.text_field :title, required: true, maxlength: 50 %>
    </div>

    <div class="field">
      <%= form.label :sub_title %>
      <%= form.text_field :sub_title, maxlength: 80 %>
    </div>

    <div class="field">
      <%= form.label :description %>
      <%= form.rich_text_area :description %>
    </div>

    <div class="field">
      <%= form.label :image %>
      <%= form.file_field :image, accept: "image/*" %>
      <% if recipe.image.attached? %>
        <p>Current image: <%= recipe.image.filename %></p>
      <% end %>
    </div>
  </div>

  <div class="form-section">
    <h3>Recipe Details</h3>

    <div class="field">
      <%= form.label :cuisine %>
      <%= form.select :cuisine,
                      options_for_select(Recipe.cuisines.map { |k, v| [k.humanize, k] }, recipe.cuisine),
                      { prompt: "Select cuisine" } %>
    </div>

    <div class="field">
      <%= form.label :protein %>
      <%= form.select :protein,
                      options_for_select(Recipe.proteins.map { |k, v| [k.humanize, k] }, recipe.protein),
                      { prompt: "Select protein type" } %>
    </div>

    <div class="field">
      <%= form.label :suggested_serves %>
      <%= form.number_field :suggested_serves, min: 1, required: true %>
    </div>
  </div>

  <div class="form-section">
    <h3>Timing</h3>

    <div class="field">
      <%= form.label :working_minutes, "Prep time (minutes)" %>
      <%= form.number_field :working_minutes, min: 0 %>
    </div>

    <div class="field">
      <%= form.label :total_minutes, "Total time (minutes)" %>
      <%= form.number_field :total_minutes, min: 0 %>
    </div>
  </div>

  <div class="form-section">
    <h3>Tags</h3>

    <div class="field">
      <%= form.label :tags, "Recipe tags" %>
      <div class="checkbox-group">
        <% Recipe.tags.each do |tag, value| %>
          <label class="checkbox-label">
            <%= check_box_tag "recipe[tags][]", value, recipe.tags&.include?(tag), id: "recipe_tags_#{tag}" %>
            <%= tag.humanize %>
          </label>
        <% end %>
      </div>
    </div>
  </div>

  <div class="form-section">
    <h3>Dietary Requirements</h3>

    <div class="field">
      <%= form.label :dietary_requirements %>
      <div class="checkbox-group">
        <% Recipe.dietary_requirements.each do |req, value| %>
          <label class="checkbox-label">
            <%= check_box_tag "recipe[dietary_requirements][]", value, recipe.dietary_requirements&.include?(req), id: "recipe_dietary_requirements_#{req}" %>
            <%= req.humanize %>
          </label>
        <% end %>
      </div>
    </div>
  </div>

  <div class="actions">
    <%= form.submit class: "btn btn-primary" %>
    <%= link_to "Cancel", recipe_path(recipe), class: "btn btn-secondary" if recipe.persisted? %>
    <%= link_to "Cancel", recipes_path, class: "btn btn-secondary" unless recipe.persisted? %>
  </div>
<% end %>
