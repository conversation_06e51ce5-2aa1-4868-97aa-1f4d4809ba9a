<div class="recipe-header">
  <h1><%= @recipe.title %></h1>
  <% if @recipe.sub_title.present? %>
    <h2><%= @recipe.sub_title %></h2>
  <% end %>
</div>

<div class="recipe-content">
  <% if @recipe.image.attached? %>
    <div class="recipe-image">
      <%= image_tag @recipe.image, alt: @recipe.title %>
    </div>
  <% end %>

  <div class="recipe-meta">
    <% if @recipe.cuisine.present? %>
      <p><strong>Cuisine:</strong> <%= @recipe.cuisine.name %></p>
    <% end %>
    <% if @recipe.protein.present? %>
      <p><strong>Protein:</strong> <%= @recipe.protein.name %></p>
    <% end %>
    <% if @recipe.working_minutes > 0 %>
      <p><strong>Prep Time:</strong> <%= @recipe.working_minutes %> minutes</p>
    <% end %>
    <% if @recipe.total_minutes > 0 %>
      <p><strong>Total Time:</strong> <%= @recipe.total_minutes %> minutes</p>
    <% end %>
    <p><strong>Serves:</strong> <%= @recipe.suggested_serves %></p>

    <% if @recipe.tags.any? %>
      <p><strong>Tags:</strong> <%= @recipe.tag_names.join(", ") %></p>
    <% end %>

    <% if @recipe.dietary_requirements.any? %>
      <p><strong>Dietary:</strong> <%= @recipe.dietary_requirement_names.join(", ") %></p>
    <% end %>
  </div>

  <% if @recipe.description.present? %>
    <div class="recipe-description">
      <h3>Description</h3>
      <%= @recipe.description %>
    </div>
  <% end %>

  <% if @recipe.instructions.present? && params[:variant_id].blank? %>
    <div class="recipe-instructions">
      <h3>Cooking Instructions</h3>
      <div class="instructions-content">
        <%= @recipe.instructions %>
      </div>
    </div>
  <% end %>

  <!-- Recipe Variants -->
  <% if @recipe.recipe_ingredients.any? || @recipe.recipe_variants.any? %>
    <div class="dietary-filter">
      <h3>Recipe Versions</h3>
      <div class="dietary-buttons">
        <%= link_to "Original Recipe", recipe_path(@recipe), class: "btn #{'btn-primary' if params[:variant_id].blank?} #{'btn-secondary' unless params[:variant_id].blank?}" %>
        <% @recipe.recipe_variants.ordered.each do |variant| %>
          <%= link_to variant.display_name, recipe_path(@recipe, variant_id: variant.id),
                      class: "btn #{'btn-primary' if params[:variant_id] == variant.id.to_s} #{'btn-secondary' unless params[:variant_id] == variant.id.to_s}" %>
        <% end %>
        <% if admin? %>
          <%= link_to "Add New Variant", new_recipe_recipe_variant_path(@recipe), class: "btn btn-outline" %>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Ingredients Section -->
  <%
    # Determine which ingredients to show
    current_variant = params[:variant_id].present? ? @recipe.recipe_variants.find(params[:variant_id]) : nil
    ingredients_to_show = current_variant ? current_variant.ingredients_with_details : @recipe.ingredients_with_details
    is_variant_view = current_variant.present?
  %>

  <div class="recipe-ingredients">
    <h3>
      Ingredients
      <% if current_variant %>
        <span class="dietary-badge">(<%= current_variant.display_name %>)</span>
      <% end %>
    </h3>

    <% if ingredients_to_show.any? %>
      <ul>
        <% ingredients_to_show.each do |ingredient_item| %>
          <li>
            <div class="ingredient-item">
              <span class="ingredient-description">
                <%= convert_ingredient_for_user(ingredient_item) %>
              </span>
              <% if admin? %>
                <div class="ingredient-actions">
                  <% if is_variant_view %>
                    <%= link_to "Edit", edit_recipe_recipe_variant_variant_ingredient_path(@recipe, current_variant, ingredient_item), class: "small-link" %>
                    <%= link_to "Remove", recipe_recipe_variant_variant_ingredient_path(@recipe, current_variant, ingredient_item),
                                data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
                                class: "small-link text-danger" %>
                  <% else %>
                    <%= link_to "Edit", edit_recipe_recipe_ingredient_path(@recipe, ingredient_item), class: "small-link" %>
                    <%= link_to "Remove", recipe_recipe_ingredient_path(@recipe, ingredient_item),
                                data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
                                class: "small-link text-danger" %>
                  <% end %>
                </div>
              <% end %>
            </div>
          </li>
        <% end %>
      </ul>
      <% if authenticated? %>
        <% if is_variant_view %>
          <%= link_to "Add Ingredient to Variant", new_recipe_recipe_variant_variant_ingredient_path(@recipe, current_variant), class: "btn btn-secondary" %>
        <% else %>
          <%= link_to "Add Ingredient", new_recipe_recipe_ingredient_path(@recipe), class: "btn btn-secondary" %>
        <% end %>
      <% end %>
    <% else %>
      <% if is_variant_view %>
        <p>No ingredients added to this variant yet.</p>
        <% if authenticated? %>
          <%= link_to "Add First Ingredient to Variant", new_recipe_recipe_variant_variant_ingredient_path(@recipe, current_variant), class: "btn btn-primary" %>
        <% end %>
      <% else %>
        <p>No ingredients added yet.</p>
        <% if authenticated? %>
          <%= link_to "Add First Ingredient", new_recipe_recipe_ingredient_path(@recipe), class: "btn btn-primary" %>
        <% end %>
      <% end %>
    <% end %>
  </div>

  <!-- Variant Instructions -->
  <% if current_variant %>
    <div class="variant-details">
      <h3>Cooking Instructions for <%= current_variant.display_name %></h3>

      <% if current_variant.working_minutes > 0 || current_variant.total_minutes > 0 %>
        <div class="variant-timing">
          <% if current_variant.working_minutes > 0 %>
            <p><strong>Prep Time:</strong> <%= current_variant.working_minutes %> minutes</p>
          <% end %>
          <% if current_variant.total_minutes > 0 %>
            <p><strong>Total Time:</strong> <%= current_variant.total_minutes %> minutes</p>
          <% end %>
        </div>
      <% end %>

      <% if current_variant.instructions.present? %>
        <div class="variant-instructions">
          <h4>Instructions:</h4>
          <div class="instructions-content">
            <%= current_variant.instructions %>
          </div>
        </div>
      <% end %>

      <% if current_variant.notes.present? %>
        <div class="variant-notes">
          <h4>Notes:</h4>
          <div class="notes-content">
            <%= simple_format(current_variant.notes) %>
          </div>
        </div>
      <% end %>

      <% if admin? %>
        <div class="variant-actions">
          <%= link_to "Edit Variant", edit_recipe_recipe_variant_path(@recipe, current_variant), class: "btn btn-secondary" %>
          <%= button_to "Delete Variant", recipe_recipe_variant_path(@recipe, current_variant),
                        method: :delete,
                        data: { turbo_confirm: "Are you sure? This will delete the entire variant and all its ingredients." },
                        class: "btn btn-danger" %>
        </div>
      <% end %>
    </div>
  <% end %>

  <!-- Rating Section -->
  <div class="recipe-rating">
    <h3>Rating</h3>
    <% if @recipe.average_rating %>
      <p>Average: <%= @recipe.average_rating %>/5 (<%= @recipe.rating_count %> ratings)</p>
    <% else %>
      <p>No ratings yet</p>
    <% end %>

    <% if authenticated? %>
      <% user_rating = @recipe.user_rating(Current.user) %>
      <% if user_rating %>
        <p>Your rating: <%= user_rating.rating %>/5</p>
        <%= form_with model: [@recipe, user_rating], local: true do |form| %>
          <%= form.select :rating, (1..5).map { |i| [i, i] }, {}, { onchange: "this.form.submit();" } %>
        <% end %>
        <%= link_to "Remove Rating", recipe_rating_path(@recipe, user_rating),
                    data: { turbo_method: :delete, turbo_confirm: "Are you sure?" } %>
      <% else %>
        <%= form_with model: [@recipe, @recipe.ratings.build], local: true do |form| %>
          <%= form.label :rating, "Rate this recipe:" %>
          <%= form.select :rating, (1..5).map { |i| [i, i] }, { prompt: "Select rating" } %>
          <%= form.submit "Rate" %>
        <% end %>
      <% end %>
    <% end %>
  </div>

  <!-- Comments Section -->
  <div class="recipe-comments">
    <h3>Comments</h3>
    <% if @recipe.recipe_comments.any? %>
      <% @recipe.recipe_comments.includes(:user).each do |comment| %>
        <div class="comment">
          <strong><%= comment.user.username %>:</strong>
          <%= comment.comment %>
          <% if authenticated? && comment.user == Current.user %>
            <%= link_to "Edit", edit_recipe_recipe_comment_path(@recipe, comment), class: "small-link" %>
            <%= link_to "Delete", recipe_recipe_comment_path(@recipe, comment),
                        data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
                        class: "small-link text-danger" %>
          <% end %>
        </div>
      <% end %>
    <% else %>
      <p>No comments yet.</p>
    <% end %>

    <% if authenticated? %>
      <% user_comment = @recipe.user_comments(Current.user) %>
      <% unless user_comment %>
        <%= form_with model: [@recipe, @recipe.recipe_comments.build], local: true do |form| %>
          <%= form.label :comment, "Add a comment:" %>
          <%= form.rich_text_area :comment, required: true %>
          <%= form.submit "Add Comment" %>
        <% end %>
      <% end %>
    <% end %>
  </div>

  <div class="recipe-actions">
    <%= link_to "Back to Recipes", recipes_path, class: "btn btn-secondary" %>
    <% if admin? %>
      <%= link_to "Edit Recipe", edit_recipe_path(@recipe), class: "btn btn-primary" %>
      <%= button_to "Delete Recipe", @recipe,
                    method: :delete,
                    data: { turbo_confirm: "Are you sure?" },
                    form_class: "button_to",
                    class: "btn btn-danger" %>
    <% end %>
  </div>
</div>
