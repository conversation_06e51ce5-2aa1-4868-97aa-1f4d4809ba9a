<div class="recipe-header">
  <h1><%= @recipe.title %></h1>
  <% if @recipe.sub_title.present? %>
    <h2><%= @recipe.sub_title %></h2>
  <% end %>
</div>

<div class="recipe-content">
  <% if @recipe.image.attached? %>
    <div class="recipe-image">
      <%= image_tag @recipe.image, alt: @recipe.title %>
    </div>
  <% end %>

  <div class="recipe-meta">
    <% if @recipe.cuisine.present? %>
      <p><strong>Cuisine:</strong> <%= @recipe.cuisine.humanize %></p>
    <% end %>
    <% if @recipe.protein.present? %>
      <p><strong>Protein:</strong> <%= @recipe.protein_vegetarian? ? "Vegetarian" : @recipe.protein.humanize %></p>
    <% end %>
    <% if @recipe.working_minutes > 0 %>
      <p><strong>Prep Time:</strong> <%= @recipe.working_minutes %> minutes</p>
    <% end %>
    <% if @recipe.total_minutes > 0 %>
      <p><strong>Total Time:</strong> <%= @recipe.total_minutes %> minutes</p>
    <% end %>
    <p><strong>Serves:</strong> <%= @recipe.suggested_serves %></p>

    <% if @recipe.tags.any? %>
      <p><strong>Tags:</strong> <%= @recipe.tag_names.join(", ") %></p>
    <% end %>

    <% if @recipe.dietary_requirements.any? %>
      <p><strong>Dietary:</strong> <%= @recipe.dietary_requirements.map(&:humanize).join(", ") %></p>
    <% end %>
  </div>

  <% if @recipe.description.present? %>
    <div class="recipe-description">
      <h3>Description</h3>
      <%= @recipe.description %>
    </div>
  <% end %>

  <!-- Dietary Requirements Filter -->
  <% if @recipe.recipe_ingredients.any? %>
    <div class="dietary-filter">
      <h3>View Recipe For Dietary Requirements</h3>
      <div class="dietary-buttons">
        <%= link_to "Original Recipe", recipe_path(@recipe), class: "btn #{'btn-primary' if params[:dietary_requirement].blank?} #{'btn-secondary' unless params[:dietary_requirement].blank?}" %>
        <% Recipe.dietary_requirements.each do |req, _| %>
          <% if @recipe.can_be_made_for_dietary_requirement?(req) %>
            <%= link_to req.humanize, recipe_path(@recipe, dietary_requirement: req),
                        class: "btn #{'btn-primary' if params[:dietary_requirement] == req} #{'btn-secondary' unless params[:dietary_requirement] == req}" %>
          <% else %>
            <span class="btn btn-disabled" title="Not available for this dietary requirement">
              <%= req.humanize %> (unavailable)
            </span>
          <% end %>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Ingredients Section -->
  <div class="recipe-ingredients">
    <h3>
      Ingredients
      <% if params[:dietary_requirement].present? %>
        <span class="dietary-badge">(<%= params[:dietary_requirement].humanize %> version)</span>
      <% end %>
    </h3>
    <% if @recipe.recipe_ingredients.any? %>
      <ul>
        <% @recipe.ingredients_with_details.each do |ri| %>
          <li>
            <div class="ingredient-item">
              <span class="ingredient-description">
                <%= ri.full_description(params[:dietary_requirement]) %>
              </span>
              <% if authenticated? %>
                <div class="ingredient-actions">
                  <%= link_to "Edit", edit_recipe_recipe_ingredient_path(@recipe, ri), class: "small-link" %>
                  <%= link_to "Remove", recipe_recipe_ingredient_path(@recipe, ri), method: :delete,
                              confirm: "Are you sure?", class: "small-link text-danger" %>
                  <% if ri.substitutions.any? %>
                    <span class="substitution-indicator" title="Has substitutions">🔄</span>
                  <% end %>
                  <%= link_to "Add Substitution", new_recipe_ingredient_ingredient_substitution_path(ri), class: "small-link" %>
                </div>
              <% end %>
            </div>

            <!-- Show existing substitutions -->
            <% if authenticated? && ri.substitutions.any? %>
              <div class="substitutions-list">
                <h5>Available Substitutions:</h5>
                <% ri.substitutions.each do |sub| %>
                  <div class="substitution-item">
                    <strong><%= sub.dietary_requirement.humanize %>:</strong>
                    <%= sub.full_substitution_description %>
                    <%= link_to "Edit", edit_recipe_ingredient_ingredient_substitution_path(ri, sub), class: "small-link" %>
                    <%= link_to "Remove", recipe_ingredient_ingredient_substitution_path(ri, sub), method: :delete,
                                confirm: "Are you sure?", class: "small-link text-danger" %>
                  </div>
                <% end %>
              </div>
            <% end %>
          </li>
        <% end %>
      </ul>
      <% if authenticated? %>
        <%= link_to "Add Ingredient", new_recipe_recipe_ingredient_path(@recipe), class: "btn btn-secondary" %>
      <% end %>
    <% else %>
      <p>No ingredients added yet.</p>
      <% if authenticated? %>
        <%= link_to "Add First Ingredient", new_recipe_recipe_ingredient_path(@recipe), class: "btn btn-primary" %>
      <% end %>
    <% end %>
  </div>

  <!-- Rating Section -->
  <div class="recipe-rating">
    <h3>Rating</h3>
    <% if @recipe.average_rating %>
      <p>Average: <%= @recipe.average_rating %>/5 (<%= @recipe.rating_count %> ratings)</p>
    <% else %>
      <p>No ratings yet</p>
    <% end %>

    <% if authenticated? %>
      <% user_rating = @recipe.user_rating(Current.user) %>
      <% if user_rating %>
        <p>Your rating: <%= user_rating.rating %>/5</p>
        <%= form_with model: [@recipe, user_rating], local: true do |form| %>
          <%= form.select :rating, (1..5).map { |i| [i, i] }, {}, { onchange: "this.form.submit();" } %>
        <% end %>
        <%= link_to "Remove Rating", recipe_rating_path(@recipe, user_rating), method: :delete,
                    confirm: "Are you sure?" %>
      <% else %>
        <%= form_with model: [@recipe, @recipe.ratings.build], local: true do |form| %>
          <%= form.label :rating, "Rate this recipe:" %>
          <%= form.select :rating, (1..5).map { |i| [i, i] }, { prompt: "Select rating" } %>
          <%= form.submit "Rate" %>
        <% end %>
      <% end %>
    <% end %>
  </div>

  <!-- Comments Section -->
  <div class="recipe-comments">
    <h3>Comments</h3>
    <% if @recipe.recipe_comments.any? %>
      <% @recipe.recipe_comments.includes(:user).each do |comment| %>
        <div class="comment">
          <strong><%= comment.user.username %>:</strong>
          <%= comment.comment %>
          <% if authenticated? && comment.user == Current.user %>
            <%= link_to "Edit", edit_recipe_recipe_comment_path(@recipe, comment), class: "small-link" %>
            <%= link_to "Delete", recipe_recipe_comment_path(@recipe, comment), method: :delete,
                        confirm: "Are you sure?", class: "small-link text-danger" %>
          <% end %>
        </div>
      <% end %>
    <% else %>
      <p>No comments yet.</p>
    <% end %>

    <% if authenticated? %>
      <% user_comment = @recipe.user_comments(Current.user) %>
      <% unless user_comment %>
        <%= form_with model: [@recipe, @recipe.recipe_comments.build], local: true do |form| %>
          <%= form.label :comment, "Add a comment:" %>
          <%= form.rich_text_area :comment, required: true %>
          <%= form.submit "Add Comment" %>
        <% end %>
      <% end %>
    <% end %>
  </div>

  <div class="recipe-actions">
    <%= link_to "Back to Recipes", recipes_path, class: "btn btn-secondary" %>
    <% if authenticated? %>
      <%= link_to "Edit Recipe", edit_recipe_path(@recipe), class: "btn btn-primary" %>
      <%= link_to "Delete Recipe", @recipe, method: :delete,
                  confirm: "Are you sure?", class: "btn btn-danger" %>
    <% end %>
  </div>
</div>
