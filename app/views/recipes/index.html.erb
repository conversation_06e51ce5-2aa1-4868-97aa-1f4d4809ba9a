<div class="recipes-header">
  <h1>Recipes</h1>
  <div class="header-actions">
    <% if admin? %>
      <%= link_to "New Recipe", new_recipe_path, class: "btn btn-primary" %>
      <%= link_to "Ingredients", ingredients_path, class: "btn btn-secondary" %>
      <%= link_to "Units", units_path, class: "btn btn-secondary" %>
    <% end %>
  </div>
</div>

<div class="recipes-grid">
  <% @recipes.each do |recipe| %>
    <div class="recipe-card">
      <% if recipe.image.attached? %>
        <%= image_tag recipe.image, alt: recipe.title, class: "recipe-card-image" %>
      <% end %>

      <div class="recipe-card-content">
        <h3><%= link_to recipe.title, recipe %></h3>

        <% if recipe.sub_title.present? %>
          <p class="recipe-subtitle"><%= recipe.sub_title %></p>
        <% end %>

        <div class="recipe-meta">
          <% if recipe.average_rating %>
            <span class="rating">★ <%= recipe.average_rating %></span>
          <% end %>

          <% if recipe.total_minutes > 0 %>
            <span class="time">🕒 <%= recipe.total_minutes %>min</span>
          <% end %>

          <% if recipe.suggested_serves > 0 %>
            <span class="serves">👥 <%= recipe.suggested_serves %></span>
          <% end %>
        </div>

        <% if recipe.tags.any? %>
          <div class="recipe-tags">
            <% recipe.tag_names.first(3).each do |tag| %>
              <span class="tag"><%= tag %></span>
            <% end %>
          </div>
        <% end %>

        <% if recipe.dietary_requirements.any? %>
          <div class="dietary-info">
            <% recipe.dietary_requirement_names.first(3).each do |req| %>
              <span class="dietary-tag"><%= req %></span>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>
</div>
