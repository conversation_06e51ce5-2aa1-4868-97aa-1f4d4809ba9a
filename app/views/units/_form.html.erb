<div class="centered-container">
  <div class="form-section">
    <%= form_with(model: unit, local: true) do |form| %>
      <% if unit.errors.any? %>
        <div id="error_explanation">
          <h2><%= pluralize(unit.errors.count, "error") %> prohibited this unit from being saved:</h2>
          <ul>
            <% unit.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <div class="field">
        <%= form.label :name %>
        <%= form.text_field :name, required: true, maxlength: 25 %>
      </div>

      <div class="field">
        <%= form.label :abbreviation %>
        <%= form.text_field :abbreviation, required: true, maxlength: 8 %>
      </div>

      <div class="field">
        <%= form.label :unit_type %>
        <%= form.select :unit_type,
                        options_for_select(Unit.unit_types.map { |k, v| [k.humanize, k] }, unit.unit_type),
                        { prompt: "Select a type" },
                        { required: true } %>
      </div>

      <div class="field">
        <%= form.label :unit_system %>
        <%= form.select :unit_system,
                        options_for_select(Unit.unit_systems.map { |k, v| [k.humanize, k] }, unit.unit_system),
                        { prompt: "Select a system" },
                        { required: true } %>
      </div>

      <div class="field checkbox-field">
        <%= form.check_box :universal %>
        <%= form.label :universal, "Universal unit (used in both metric and imperial)", for: "universal_checkbox" %>
      </div>

      <div class="field">
        <%= form.label :measure %>
        <%= form.number_field :measure, step: 0.0001, min: 0.0001, required: true %>
        <small>Base measure for conversion (grams and ml for metric, ounces and fluid ounces for imperial)</small>
      </div>

      <div class="actions">
        <%= form.submit class: "btn btn-primary" %>
        <%= link_to "Cancel", units_path, class: "btn btn-secondary" %>
      </div>
    <% end %>
  </div>
</div>
