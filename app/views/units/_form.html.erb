<%= form_with(model: unit, local: true) do |form| %>
  <% if unit.errors.any? %>
    <div id="error_explanation">
      <h2><%= pluralize(unit.errors.count, "error") %> prohibited this unit from being saved:</h2>
      <ul>
        <% unit.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="field">
    <%= form.label :name %>
    <%= form.text_field :name, required: true, maxlength: 25 %>
  </div>

  <div class="field">
    <%= form.label :abbreviation %>
    <%= form.text_field :abbreviation, required: true, maxlength: 8 %>
  </div>

  <div class="field">
    <%= form.label :unit_type %>
    <%= form.select :unit_type, 
                    options_for_select(Unit.unit_types.map { |k, v| [k.humanize, k] }, unit.unit_type),
                    { prompt: "Select a type" },
                    { required: true } %>
  </div>

  <div class="field">
    <%= form.label :measure %>
    <%= form.number_field :measure, step: 0.0001, min: 0.0001, required: true %>
    <small>Base measure for conversion (e.g., 1 for base unit, 1000 for kg to g)</small>
  </div>

  <div class="actions">
    <%= form.submit %>
    <%= link_to "Cancel", unit_path(unit), class: "btn btn-secondary" if unit.persisted? %>
    <%= link_to "Cancel", units_path, class: "btn btn-secondary" unless unit.persisted? %>
  </div>
<% end %>
