<div class="centered-container">
  <div class="header">
    <h1><%= @unit.name %> (<%= @unit.abbreviation %>)</h1>
    <p><%= @unit.unit_type.humanize %> unit in the <%= @unit.unit_system.humanize %> system</p>
  </div>

  <div class="form-section">
    <h3>Unit Details</h3>
    <div class="unit-details">
      <span><strong>Abbreviation:</strong> <%= @unit.abbreviation %></span>
      <span><strong>Type:</strong> <%= @unit.unit_type.humanize %></span>
      <span><strong>System:</strong> <%= @unit.unit_system.humanize %></span>
      <span><strong>Measure:</strong> <%= @unit.measure %></span>
      <% if @unit.universal? %>
        <span class="universal-badge">Universal</span>
      <% end %>
    </div>
  </div>

  <% if admin? %>
    <div class="form-section">
      <h3>Actions</h3>
      <div class="actions">
        <%= link_to 'Edit', edit_unit_path(@unit), class: "btn btn-primary" %>
        <% if @unit.recipe_ingredients.empty? %>
          <%= link_to 'Delete', @unit,
                      data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
                      class: "btn btn-danger" %>
        <% else %>
          <span class="text-muted">Cannot delete - used in <%= @unit.recipe_ingredients.count %> recipe ingredients</span>
        <% end %>
        <%= link_to 'Back to Units', units_path, class: "btn btn-secondary" %>
      </div>
    </div>
  <% else %>
    <div class="actions">
      <%= link_to 'Back to Units', units_path, class: "btn btn-secondary" %>
    </div>
  <% end %>

  <% if @unit.recipe_ingredients.any? %>
    <div class="form-section">
      <h3>Used in Recipes (<%= @unit.recipe_ingredients.includes(:recipe).map(&:recipe).uniq.count %>)</h3>
      <div class="recipe-list">
        <% @unit.recipe_ingredients.includes(:recipe).map(&:recipe).uniq.each do |recipe| %>
          <div class="recipe-link">
            <%= link_to recipe.title, recipe, class: "btn btn-outline" %>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>
</div>
