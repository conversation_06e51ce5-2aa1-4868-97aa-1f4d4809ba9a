<h1><%= @unit.name %></h1>

<p><strong>Abbreviation:</strong> <%= @unit.abbreviation %></p>
<p><strong>Type:</strong> <%= @unit.unit_type.humanize %></p>
<p><strong>Measure:</strong> <%= @unit.measure %></p>

<div class="actions">
  <%= link_to 'Edit', edit_unit_path(@unit), class: "btn btn-primary" %>
  <% if @unit.recipe_ingredients.empty? %>
    <%= link_to 'Delete', @unit,
                data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
                class: "btn btn-danger" %>
  <% else %>
    <span class="text-muted">Cannot delete - used in recipes</span>
  <% end %>
  <%= link_to 'Back', units_path, class: "btn btn-secondary" %>
</div>

<% if @unit.recipe_ingredients.any? %>
  <h3>Used in Recipes:</h3>
  <ul>
    <% @unit.recipe_ingredients.includes(:recipe).each do |ri| %>
      <li><%= link_to ri.recipe.title, ri.recipe %></li>
    <% end %>
  </ul>
<% end %>
