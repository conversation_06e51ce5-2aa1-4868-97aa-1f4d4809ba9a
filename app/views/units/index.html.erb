<div class="centered-container">
  <div class="header">
    <h1>Units</h1>
    <p>Manage measurement units for recipes</p>
  </div>

  <% if admin? %>
    <div class="add-form">
      <%= link_to "Add New Unit", new_unit_path, class: "btn btn-primary" %>
    </div>
  <% end %>

  <% if @units.any? %>
    <div class="units-content">
      <% Unit.unit_types.each do |type, _| %>
        <% units_of_type = @units.select { |u| u.unit_type == type } %>
        <% if units_of_type.any? %>
          <div class="unit-section">
            <h2><%= type.humanize %> Units (<%= units_of_type.count %>)</h2>
            <div class="units-list">
              <% units_of_type.each do |unit| %>
                <div class="unit-card">
                  <div class="card-content">
                    <h3><%= unit.name %></h3>
                    <div class="unit-details">
                      <span class="abbreviation"><strong>Abbreviation:</strong> <%= unit.abbreviation %></span>
                      <span class="measure"><strong>System:</strong> <%= unit.unit_system.humanize %></span>
                      <% if unit.universal? %>
                        <span class="universal-badge">Universal</span>
                      <% end %>
                    </div>
                  </div>
                  <% if admin? %>
                    <div class="card-actions">
                      <%= link_to "Edit", edit_unit_path(unit), class: "btn btn-sm btn-secondary" %>
                      <%= link_to "Delete", unit,
                                  data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
                                  class: "btn btn-sm btn-danger" %>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
  <% else %>
    <div class="empty-state">
      <h3>No units yet</h3>
      <p>Start building your unit library</p>
      <% if admin? %>
        <%= link_to "Add First Unit", new_unit_path, class: "btn btn-primary" %>
      <% end %>
    </div>
  <% end %>
</div>
