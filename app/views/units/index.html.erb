<h1>Units</h1>

<% if admin? %>
  <%= link_to "New Unit", new_unit_path, class: "btn btn-primary" %>
<% end %>

<div class="units-list">
  <% Unit.unit_types.each do |type, _| %>
    <h3><%= type.humanize %></h3>
    <% @units.select { |u| u.unit_type == type }.each do |unit| %>
      <div class="unit-card">
        <h4><%= link_to unit.name, unit %></h4>
        <p>
          <strong>Abbreviation:</strong> <%= unit.abbreviation %><br>
          <strong>Measure:</strong> <%= unit.measure %>
        </p>
        <% if admin? %>
          <div class="actions">
            <%= link_to "Edit", edit_unit_path(unit) %>
            <%= link_to "Delete", unit,
                        data: { turbo_method: :delete, turbo_confirm: "Are you sure?" },
                        class: "text-danger" %>
          </div>
        <% end %>
      </div>
    <% end %>
  <% end %>
</div>

<% if @units.empty? %>
  <p>No units found. <%= link_to "Create one", new_unit_path %>!</p>
<% end %>
