class VariantIngredientsController < ApplicationController
  before_action :set_recipe_and_variant
  before_action :set_variant_ingredient, only: [ :edit, :update, :destroy ]
  before_action :require_admin

  def new
    @variant_ingredient = @variant.variant_ingredients.build
    @variant_ingredient.display_order = (@variant.variant_ingredients.maximum(:display_order) || 0) + 1
  end

  def create
    @variant_ingredient = @variant.variant_ingredients.build(variant_ingredient_params)

    if @variant_ingredient.save
      redirect_to recipe_path(@recipe), notice: "Ingredient was successfully added to variant."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @variant_ingredient.update(variant_ingredient_params)
      redirect_to recipe_path(@recipe), notice: "Variant ingredient was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @variant_ingredient.destroy
    redirect_to recipe_path(@recipe), notice: "Ingredient was successfully removed from variant."
  end

  private

  def set_recipe_and_variant
    @recipe = Recipe.find(params[:recipe_id])
    @variant = @recipe.recipe_variants.find(params[:recipe_variant_id])
  end

  def set_variant_ingredient
    @variant_ingredient = @variant.variant_ingredients.find(params[:id])
  end

  def variant_ingredient_params
    params.require(:variant_ingredient).permit(:ingredient_id, :unit_id, :amount, :preparation, :notes, :display_order)
  end
end
