class ApplicationController < ActionController::Base
  include Authentication
  include UnitConversionHelper
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern

  protected

  def require_admin
    unless admin?
      redirect_to root_path, alert: "Access denied. Admin privileges required."
    end
  end
end
