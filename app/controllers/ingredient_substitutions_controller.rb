class IngredientSubstitutionsController < ApplicationController
  before_action :set_recipe_ingredient
  before_action :set_substitution, only: [ :edit, :update, :destroy ]

  def new
    @substitution = @recipe_ingredient.substitutions.build
  end

  def create
    @substitution = @recipe_ingredient.substitutions.build(substitution_params)

    if @substitution.save
      redirect_to @recipe_ingredient.recipe, notice: "Ingredient substitution was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @substitution.update(substitution_params)
      redirect_to @recipe_ingredient.recipe, notice: "Ingredient substitution was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @substitution.destroy
    redirect_to @recipe_ingredient.recipe, notice: "Ingredient substitution was successfully removed."
  end

  private

  def set_recipe_ingredient
    @recipe_ingredient = RecipeIngredient.find(params[:recipe_ingredient_id])
    @recipe = @recipe_ingredient.recipe
  end

  def set_substitution
    @substitution = @recipe_ingredient.substitutions.find(params[:id])
  end

  def substitution_params
    params.require(:ingredient_substitution).permit(
      :substitute_ingredient_id, :substitute_unit_id, :substitute_amount,
      :substitute_preparation, :dietary_requirement, :substitute_notes
    )
  end
end
