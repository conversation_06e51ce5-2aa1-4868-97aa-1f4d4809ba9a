class Admin::AdminController < ApplicationController
  before_action :require_admin

  def index
    # Load data for all admin sections
    @cuisines = Cuisine.order(:name)
    @proteins = Protein.order(:name)
    @tags = Tag.order(:name)
    @dietary_requirements = DietaryRequirement.order(:name)
    @ingredients = Ingredient.order(:name)
    @units = Unit.order(:unit_type, :name)
  end

  def create
    model_class = params[:type].constantize
    item = model_class.new(item_params)

    if item.save
      redirect_to admin_root_path, notice: "#{params[:type]} was successfully created."
    else
      redirect_to admin_root_path, alert: "Error creating #{params[:type]}: #{item.errors.full_messages.join(', ')}"
    end
  end

  def toggle_active
    model_class = params[:type].constantize
    item = model_class.find(params[:id])
    item.update!(active: !item.active)
    redirect_to admin_root_path, notice: "#{params[:type]} was successfully #{item.active? ? 'activated' : 'deactivated'}."
  end

  def destroy
    model_class = params[:type].constantize
    item = model_class.find(params[:id])
    item.destroy!
    redirect_to admin_root_path, notice: "#{params[:type]} was successfully deleted."
  end

  private

  def item_params
    params.require(:item).permit(:name, :description)
  end

  def require_admin
    redirect_to root_path unless admin?
  end
end
