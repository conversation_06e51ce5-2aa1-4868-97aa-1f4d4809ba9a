class Admin::UnitsController < ApplicationController
  before_action :set_unit, only: [:edit, :update, :destroy]
  before_action :require_admin

  def index
    redirect_to admin_root_path
  end

  def new
    @unit = Unit.new
  end

  def create
    @unit = Unit.new(unit_params)

    if @unit.save
      redirect_to admin_root_path, notice: "Unit was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @unit.update(unit_params)
      redirect_to admin_root_path, notice: "Unit was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @unit.destroy!
    redirect_to admin_root_path, notice: "Unit was successfully deleted."
  end

  private

  def set_unit
    @unit = Unit.find(params[:id])
  end

  def unit_params
    params.require(:unit).permit(:name, :abbreviation, :unit_type, :unit_system, :universal, :measure)
  end

  def require_admin
    redirect_to root_path unless admin?
  end
end
