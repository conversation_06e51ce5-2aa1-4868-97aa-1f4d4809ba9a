class Admin::EnumsController < ApplicationController
  before_action :require_admin

  def index
    @cuisines = Cuisine.alphabetical
    @proteins = Protein.alphabetical
    @tags = Tag.alphabetical
    @dietary_requirements = DietaryRequirement.alphabetical
  end

  def toggle_active
    model_class = params[:type].constantize
    item = model_class.find(params[:id])
    item.update!(active: !item.active)

    redirect_to admin_enums_path, notice: "#{params[:type]} #{item.active? ? 'activated' : 'deactivated'} successfully."
  rescue => e
    redirect_to admin_enums_path, alert: "Error: #{e.message}"
  end

  def create
    model_class = params[:type].constantize
    item = model_class.new(item_params)

    if item.save
      redirect_to admin_enums_path, notice: "#{params[:type]} created successfully."
    else
      redirect_to admin_enums_path, alert: "Error: #{item.errors.full_messages.join(', ')}"
    end
  rescue => e
    redirect_to admin_enums_path, alert: "Error: #{e.message}"
  end

  def destroy
    model_class = params[:type].constantize
    item = model_class.find(params[:id])

    if item.destroy
      redirect_to admin_enums_path, notice: "#{params[:type]} deleted successfully."
    else
      redirect_to admin_enums_path, alert: "Error: #{item.errors.full_messages.join(', ')}"
    end
  rescue ActiveRecord::InvalidForeignKey => e
    redirect_to admin_enums_path, alert: "Cannot delete #{params[:type].downcase} - it's being used by existing recipes. Deactivate it instead."
  rescue => e
    redirect_to admin_enums_path, alert: "Error: #{e.message}"
  end

  private



  def item_params
    params.require(:item).permit(:name, :description)
  end
end
