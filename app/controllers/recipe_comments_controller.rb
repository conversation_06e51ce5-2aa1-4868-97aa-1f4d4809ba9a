class RecipeCommentsController < ApplicationController
  before_action :set_recipe
  before_action :set_comment, only: [ :edit, :update, :destroy ]

  def create
    @comment = @recipe.recipe_comments.build(comment_params)
    @comment.user = Current.user

    if @comment.save
      redirect_to @recipe, notice: "Comment was successfully added."
    else
      redirect_to @recipe, alert: "Unable to save comment."
    end
  end

  def edit
  end

  def update
    if @comment.update(comment_params)
      redirect_to @recipe, notice: "Comment was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @comment.destroy
    redirect_to @recipe, notice: "Comment was successfully removed."
  end

  private

  def set_recipe
    @recipe = Recipe.find(params[:recipe_id])
  end

  def set_comment
    @comment = @recipe.recipe_comments.find_by(user: Current.user)
  end

  def comment_params
    params.require(:recipe_comment).permit(:comment)
  end
end
