class RecipesController < ApplicationController
  allow_unauthenticated_access only: %i[ index show ]
  before_action :set_recipe, only: %i[ show edit update destroy ]

  def index
    @recipes = Recipe.all
  end

  def show
  end

  def new
    @recipe = Recipe.new
  end

  def create
    @recipe = Recipe.new(recipe_params)

    if @recipe.save
      redirect_to @recipe
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @recipe.update(recipe_params)
      redirect_to @recipe
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @recipe.destroy
    redirect_to recipes_path, notice: "Recipe was successfully deleted."
  end

  private
    def set_recipe
      @recipe = Recipe.find(params[:id])
    end

    def recipe_params
      permitted_params = params.require(:recipe).permit(
        :title, :sub_title, :description, :image, :cuisine, :protein,
        :working_minutes, :total_minutes, :suggested_serves,
        tags: [], dietary_requirements: []
      )

      # Convert string arrays to integer arrays for enums
      if permitted_params["tags"].present?
        permitted_params["tags"] = permitted_params["tags"].reject(&:blank?).map(&:to_i)
      end

      if permitted_params["dietary_requirements"].present?
        permitted_params["dietary_requirements"] = permitted_params["dietary_requirements"].reject(&:blank?).map(&:to_i)
      end

      permitted_params
    end
end
