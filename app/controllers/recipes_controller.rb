class RecipesController < ApplicationController
  allow_unauthenticated_access only: %i[ index show ]
  before_action :set_recipe, only: %i[ show edit update destroy ]

  def index
    @recipes = Recipe.all
  end

  def show
  end

  def new
    @recipe = Recipe.new
  end

  def create
    @recipe = Recipe.new(recipe_params)

    if @recipe.save
      redirect_to @recipe
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @recipe.update(recipe_params)
      redirect_to @recipe
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @recipe.destroy
    redirect_to recipes_path, notice: "Recipe was successfully deleted."
  end

  private
    def set_recipe
      @recipe = Recipe.find(params[:id])
    end

    def recipe_params
      permitted_params = params.require(:recipe).permit(
        :title, :sub_title, :description, :instructions, :image, :cuisine_id, :protein_id,
        :working_minutes, :total_minutes, :suggested_serves,
        tag_ids: [], dietary_requirement_ids: []
      )

      # Clean up empty arrays - remove empty strings that come from hidden fields
      permitted_params[:tag_ids] = permitted_params[:tag_ids]&.reject(&:blank?) || []
      permitted_params[:dietary_requirement_ids] = permitted_params[:dietary_requirement_ids]&.reject(&:blank?) || []

      permitted_params
    end
end
