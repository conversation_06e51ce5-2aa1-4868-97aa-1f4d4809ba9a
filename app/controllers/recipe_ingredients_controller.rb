class RecipeIngredientsController < ApplicationController
  before_action :set_recipe
  before_action :set_recipe_ingredient, only: [ :edit, :update, :destroy ]

  def new
    @recipe_ingredient = @recipe.recipe_ingredients.build
  end

  def create
    @recipe_ingredient = @recipe.recipe_ingredients.build(recipe_ingredient_params)

    if @recipe_ingredient.save
      redirect_to @recipe, notice: "Ingredient was successfully added to recipe."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @recipe_ingredient.update(recipe_ingredient_params)
      redirect_to @recipe, notice: "Recipe ingredient was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @recipe_ingredient.destroy
    redirect_to @recipe, notice: "Ingredient was successfully removed from recipe."
  end

  private

  def set_recipe
    @recipe = Recipe.find(params[:recipe_id])
  end

  def set_recipe_ingredient
    @recipe_ingredient = @recipe.recipe_ingredients.find(params[:id])
  end

  def recipe_ingredient_params
    params.require(:recipe_ingredient).permit(:ingredient_id, :unit_id, :amount, :preparation, :notes, :display_order)
  end
end
