class RecipeVariantsController < ApplicationController
  before_action :require_authentication
  before_action :set_recipe
  before_action :set_variant, only: [ :show, :edit, :update, :destroy ]

  def show
    @variant_ingredients = @variant.ingredients_with_details
  end

  def new
    @variant = @recipe.recipe_variants.build
  end

  def create
    @variant = @recipe.recipe_variants.build(variant_params)

    if @variant.save
      redirect_to recipe_path(@recipe), notice: "Recipe variant was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @variant.update(variant_params)
      redirect_to recipe_path(@recipe), notice: "Recipe variant was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @variant.destroy
    redirect_to recipe_path(@recipe), notice: "Recipe variant was successfully deleted."
  end

  private

  def set_recipe
    @recipe = Recipe.find(params[:recipe_id])
  end

  def set_variant
    @variant = @recipe.recipe_variants.find(params[:id])
  end

  def variant_params
    params.require(:recipe_variant).permit(:dietary_requirement, :name, :instructions, :working_minutes, :total_minutes, :notes)
  end
end
