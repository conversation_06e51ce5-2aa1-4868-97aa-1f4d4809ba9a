class UsersController < ApplicationController
  before_action :set_user

  def edit
  end

  def update
    if @user.update(user_params)
      redirect_to edit_user_path, notice: "Preferences updated successfully."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def set_user
    @user = Current.session.user
  end

  def user_params
    params.require(:user).permit(:unit_preference)
  end
end
