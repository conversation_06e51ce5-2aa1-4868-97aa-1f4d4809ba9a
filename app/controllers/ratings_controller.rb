class RatingsController < ApplicationController
  before_action :set_recipe
  before_action :set_rating, only: [ :update, :destroy ]

  def create
    @rating = @recipe.ratings.build(rating_params)
    @rating.user = Current.user

    if @rating.save
      redirect_to @recipe, notice: "Rating was successfully created."
    else
      redirect_to @recipe, alert: "Unable to save rating."
    end
  end

  def update
    if @rating.update(rating_params)
      redirect_to @recipe, notice: "Rating was successfully updated."
    else
      redirect_to @recipe, alert: "Unable to update rating."
    end
  end

  def destroy
    @rating.destroy
    redirect_to @recipe, notice: "Rating was successfully removed."
  end

  private

  def set_recipe
    @recipe = Recipe.find(params[:recipe_id])
  end

  def set_rating
    @rating = @recipe.ratings.find_by(user: Current.user)
  end

  def rating_params
    params.require(:rating).permit(:rating)
  end
end
