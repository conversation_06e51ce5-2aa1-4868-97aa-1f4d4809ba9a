module UnitConversionHelper
  # Convert an ingredient amount to the user's preferred unit system
  def convert_ingredient_for_user(recipe_ingredient, user = Current.session&.user)
    return format_ingredient_display(recipe_ingredient) unless user&.unit_preference

    original_unit = recipe_ingredient.unit
    original_amount = recipe_ingredient.amount

    # If the unit is universal, no conversion needed
    return format_ingredient_display(recipe_ingredient) if original_unit.universal?

    # If the unit is already in the user's preferred system, no conversion needed
    return format_ingredient_display(recipe_ingredient) if original_unit.unit_system == user.unit_preference

    # Find equivalent unit in user's preferred system
    target_unit = original_unit.equivalent_unit_in_system(user.unit_preference)
    return format_ingredient_display(recipe_ingredient) unless target_unit

    # Convert the amount
    converted_amount = convert_between_systems(original_amount, original_unit, target_unit)
    
    # Format the display with converted values
    format_converted_ingredient_display(converted_amount, target_unit, recipe_ingredient)
  end

  private

  # Convert between metric and imperial systems
  def convert_between_systems(amount, from_unit, to_unit)
    # Conversion factors between metric and imperial
    conversions = {
      # Weight conversions (grams <-> ounces)
      ['weight', 'metric', 'imperial'] => 0.035274, # grams to ounces
      ['weight', 'imperial', 'metric'] => 28.3495,  # ounces to grams
      
      # Volume conversions (ml <-> fl oz)
      ['volume', 'metric', 'imperial'] => 0.033814, # ml to fl oz
      ['volume', 'imperial', 'metric'] => 29.5735,  # fl oz to ml
      
      # Temperature conversions
      ['temperature', 'metric', 'imperial'] => ->(c) { (c * 9.0/5.0) + 32 }, # C to F
      ['temperature', 'imperial', 'metric'] => ->(f) { (f - 32) * 5.0/9.0 }   # F to C
    }

    conversion_key = [from_unit.unit_type, from_unit.unit_system, to_unit.unit_system]
    conversion_factor = conversions[conversion_key]

    return amount unless conversion_factor

    if conversion_factor.is_a?(Proc)
      # Temperature conversion (special case)
      conversion_factor.call(amount)
    else
      # Standard multiplication conversion
      # First convert to base unit of original system
      base_amount = amount * from_unit.measure
      # Convert to other system's base unit
      converted_base = base_amount * conversion_factor
      # Convert to target unit
      converted_base / to_unit.measure
    end
  end

  # Format the original ingredient display
  def format_ingredient_display(recipe_ingredient)
    amount = format_amount(recipe_ingredient.amount)
    unit = recipe_ingredient.unit.abbreviation
    ingredient = recipe_ingredient.ingredient.name
    preparation = recipe_ingredient.preparation&.humanize

    display = "#{amount} #{unit} #{ingredient}"
    display += " (#{preparation})" if preparation.present?
    display
  end

  # Format the converted ingredient display
  def format_converted_ingredient_display(converted_amount, target_unit, recipe_ingredient)
    amount = format_amount(converted_amount)
    unit = target_unit.abbreviation
    ingredient = recipe_ingredient.ingredient.name
    preparation = recipe_ingredient.preparation&.humanize

    display = "#{amount} #{unit} #{ingredient}"
    display += " (#{preparation})" if preparation.present?
    
    # Add original amount in parentheses for reference
    original_display = format_ingredient_display(recipe_ingredient)
    display += " <span class='text-muted small'>[orig: #{format_amount(recipe_ingredient.amount)} #{recipe_ingredient.unit.abbreviation}]</span>"
    
    display.html_safe
  end

  # Format amount to remove unnecessary decimals
  def format_amount(amount)
    if amount == amount.to_i
      amount.to_i.to_s
    else
      # Round to 2 decimal places and remove trailing zeros
      ("%.2f" % amount).sub(/\.?0+$/, '')
    end
  end
end
