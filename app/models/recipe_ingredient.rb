class RecipeIngredient < ApplicationRecord
  belongs_to :recipe
  belongs_to :ingredient
  belongs_to :unit
  has_many :substitutions, class_name: "IngredientSubstitution", dependent: :destroy

  enum preperation: {
    raw: 0,
    cooked: 1,
    roasted: 2,
    grilled: 3,
    fried: 4,
    baked: 5,
    boiled: 6,
    steamed: 7,
    sauteed: 8,
    stir_fried: 9,
    marinated: 10,
    seasoned: 11,
    chopped: 12,
    diced: 13 }

  validates :amount, presence: true, numericality: { greater_than: 0 }
  validates :display_order, presence: true, numericality: { greater_than: 0 }

  def substitution_for(dietary_requirement)
    substitutions.find_by(dietary_requirement: dietary_requirement)
  end

  def has_substitution_for?(dietary_requirement)
    substitutions.exists?(dietary_requirement: dietary_requirement)
  end

  def full_description(dietary_requirement = nil)
    if dietary_requirement && (sub = substitution_for(dietary_requirement))
      sub.full_substitution_description
    else
      parts = []
      parts << "#{amount} #{unit.abbreviation || unit.name}"
      parts << ingredient.name
      parts << "(#{preparation.humanize})" if preparation.present?
      parts.join(" ")
    end
  end
end
