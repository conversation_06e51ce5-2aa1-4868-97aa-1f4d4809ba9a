class User < ApplicationRecord
  has_secure_password
  has_many :sessions, dependent: :destroy

  has_many :ratings, dependent: :nullify
  has_many :recipe_comments, dependent: :destroy
  has_many :rated_recipes, through: :ratings, source: :recipe

  enum :subscription_level, {
    free: 0,
    basic: 1,
    premium: 2
  }

  validates :username, presence: true, uniqueness: true
  validates :email_address, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :subscription_level, presence: true
  validates :admin, inclusion: { in: [ true, false ] }

  normalizes :email_address, with: ->(e) { e.strip.downcase }
end
