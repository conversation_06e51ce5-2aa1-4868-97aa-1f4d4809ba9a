class VariantIngredient < ApplicationRecord
  belongs_to :recipe_variant
  belongs_to :ingredient
  belongs_to :unit

  enum :preparation, {
    chopped: 0,
    diced: 1,
    minced: 2,
    sliced: 3,
    shredded: 4,
    crushed: 5,
    grated: 6,
    cubed: 7,
    whole: 8,
    peeled: 9,
    quartered: 10,
    halved: 11,
    julienned: 12,
    thinly_sliced: 13,
    thickly_sliced: 14,
    crumbled: 15,
    pureed: 16,
    blended: 17,
    roasted: 18
  }

  validates :amount, presence: true, numericality: { greater_than: 0 }
  validates :display_order, presence: true, numericality: { greater_than: 0 }

  scope :ordered, -> { order(:display_order) }

  def full_description
    parts = []
    parts << "#{amount} #{unit.abbreviation || unit.name}"
    parts << ingredient.name
    parts << "(#{preparation.humanize})" if preparation.present?
    parts << "- #{notes}" if notes.present?
    parts.join(" ")
  end
end
