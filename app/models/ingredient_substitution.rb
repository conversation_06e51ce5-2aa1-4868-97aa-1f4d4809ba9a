class IngredientSubstitution < ApplicationRecord
  belongs_to :recipe_ingredient
  belongs_to :substitute_ingredient, class_name: "Ingredient"
  belongs_to :substitute_unit, class_name: "Unit"

  enum :dietary_requirement, {
    gluten_free: 0,
    dairy_free: 1,
    nut_free: 2,
    vegan: 3,
    vegetarian: 4,
    kosher: 5,
    halal: 6,
    paleo: 7,
    low_carb: 8 }

  enum :substitute_preparation, {
    chopped: 0,
    diced: 1,
    minced: 2,
    sliced: 3,
    shredded: 4,
    crushed: 5,
    grated: 6,
    cubed: 7,
    whole: 8,
    peeled: 9,
    quartered: 10,
    halved: 11,
    julienned: 12,
    thinly_sliced: 13,
    thickly_sliced: 14,
    crumbled: 15,
    pureed: 16,
    blended: 17,
    roasted: 18 }

  validates :substitute_amount, presence: true, numericality: { greater_than: 0 }
  validates :dietary_requirement, presence: true
  validates :recipe_ingredient_id, uniqueness: { scope: :dietary_requirement }

  def full_substitution_description
    parts = []
    parts << "#{substitute_amount} #{substitute_unit.abbreviation || substitute_unit.name}"
    parts << substitute_ingredient.name
    parts << "(#{substitute_preparation.humanize})" if substitute_preparation.present?
    parts.join(" ")
  end
end
