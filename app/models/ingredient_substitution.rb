class IngredientSubstitution < ApplicationRecord
  belongs_to :recipe_ingredient
  belongs_to :substitute_ingredient, class_name: "Ingredient"
  belongs_to :substitute_unit, class_name: "Unit"

  enum dietary_requirement: {
    gluten_free: 0,
    dairy_free: 1,
    nut_free: 2,
    vegan: 3,
    vegetarian: 4,
    kosher: 5,
    halal: 6,
    paleo: 7,
    low_carb: 8 }

  enum substitute_preperation: {
    raw: 0,
    cooked: 1,
    roasted: 2,
    grilled: 3,
    fried: 4,
    baked: 5,
    boiled: 6,
    steamed: 7,
    sauteed: 8,
    stir_fried: 9,
    marinated: 10,
    seasoned: 11,
    chopped: 12 }

  validates :substitute_amount, presence: true, numericality: { greater_than: 0 }
  validates :dietary_requirement, presence: true
  validates :recipe_ingredient_id, uniqueness: { scope: :dietary_requirement }

  def full_substitution_description
    parts = []
    parts << "#{substitute_amount} #{substitute_unit.abbreviation || substitute_unit.name}"
    parts << substitute_ingredient.name
    parts << "(#{substitute_preparation.humanize})" if substitute_preparation.present?
    parts.join(" ")
  end
end
