class Unit < ApplicationRecord
  has_many :recipe_ingredients, dependent: :restrict_with_error

  enum :unit_type, {
    weight: 0,
    volume: 1,
    temperature: 2
  }

  validates :name, presence: true, uniqueness: true, length: { maximum: 25 }
  validates :abbreviation, presence: true, uniqueness: true, length: { maximum: 8 }
  validates :unit_type, presence: true
  validates :measure, presence: true, numericality: { greater_than: 0, less_than_or_equal_to: 99_999_999.9999 }
end
