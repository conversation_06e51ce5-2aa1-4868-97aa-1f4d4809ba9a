class Unit < ApplicationRecord
  has_many :recipe_ingredients, dependent: :restrict_with_error

  enum :unit_type, {
    weight: 0,
    volume: 1,
    temperature: 2
  }

  enum :unit_system, {
    metric: 0,
    imperial: 1
  }

  validates :name, presence: true, uniqueness: true, length: { maximum: 25 }
  validates :abbreviation, presence: true, uniqueness: true, length: { maximum: 8 }
  validates :unit_type, presence: true
  validates :unit_system, presence: true
  validates :universal, inclusion: { in: [ true, false ] }
  validates :measure, presence: true, numericality: { greater_than: 0, less_than_or_equal_to: 99_999_999.9999 }

  # Scope for units that match a user's preference or are universal
  scope :for_user_preference, ->(user_preference) {
    where(universal: true).or(where(unit_system: user_preference))
  }

  # Find the best unit for a user's preference within the same unit type
  def self.preferred_unit_for(unit_type, user_preference)
    for_user_preference(user_preference).where(unit_type: unit_type).first
  end

  # Convert an amount from this unit to another unit of the same type
  def convert_to(target_unit, amount)
    return amount if self == target_unit
    return amount if universal? && target_unit.universal?

    # Convert to base unit (measure represents conversion to base)
    base_amount = amount * measure

    # Convert from base unit to target unit
    base_amount / target_unit.measure
  end

  # Get the equivalent unit in the opposite system (metric <-> imperial)
  def equivalent_unit_in_system(target_system)
    return self if universal? || unit_system == target_system.to_s

    Unit.where(unit_type: unit_type, unit_system: target_system).first
  end

  # Check if this unit can be converted to another unit
  def convertible_to?(other_unit)
    unit_type == other_unit.unit_type
  end
end
