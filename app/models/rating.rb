class Rating < ApplicationRecord
  belongs_to :user
  belongs_to :recipe

  validates :rating, presence: true, numericality: { greater_than_or_equal_to: 1, less_than_or_equal_to: 5 }
  validates :user_id, uniqueness: { scope: :recipe_id }

  after_save :update_recipe_average_rating
  after_destroy :update_recipe_average_rating

  private

  def update_recipe_average_rating
    recipe.update_average_rating!
  end
end
