class Recipe < ApplicationRecord
  has_one_attached :image
  has_rich_text :description

  has_many :recipe_ingredients, dependent: :destroy
  has_many :ingredients, through: :recipe_ingredients
  has_many :ratings, dependent: :destroy
  has_many :recipe_comments, dependent: :destroy
  has_many :users, through: :ratings

  enum :cuisine, {
    african: 0,
    asian: 1,
    european: 2,
    latin_american: 3,
    middle_eastern: 4,
    north_american: 5,
    oceanic: 6,
    south_american: 7
  }

  enum :protein, {
    beef: 0,
    chicken: 1,
    fish: 2,
    lamb: 3,
    pork: 4,
    poultry: 5,
    seafood: 6,
    vegetarian: 7,
    vegan: 8
  }, prefix: :protein

  # Handle tags and dietary_requirements as arrays manually since Rails 8.0 multiple enums work differently
  TAGS = {
    camping: 0,
    bbq: 1,
    cheap: 2,
    quick: 3
  }.freeze

  DIETARY_REQUIREMENTS = {
    vegetarian: 0,
    vegan: 1,
    gluten_free: 2,
    dairy_free: 3,
    keto: 4,
    paleo: 5,
    low_carb: 6,
    nut_free: 7
  }.freeze

  # Helper methods for tags
  def tag_names
    return [] if tags.blank?
    tags.map { |tag_value| TAGS.key(tag_value.to_i)&.to_s&.humanize }.compact
  end

  def self.tags
    TAGS
  end

  # Helper methods for dietary requirements
  def dietary_requirement_names
    return [] if dietary_requirements.blank?
    dietary_requirements.map { |req_value| DIETARY_REQUIREMENTS.key(req_value.to_i)&.to_s&.humanize }.compact
  end

  def self.dietary_requirements
    DIETARY_REQUIREMENTS
  end

  validates :title, presence: true, length: { maximum: 50 }, uniqueness: true
  validates :sub_title, length: { maximum: 80 }
  validates :working_minutes, :total_minutes, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :suggested_serves, presence: true, numericality: { greater_than: 0 }, allow_nil: false
  validate :total_minutes_greater_equal_than_working_minutes

  scope :with_dietary_requirements, ->(requirements) {
    where("dietary_requirements && ARRAY[?]::integer[]",
          Array(requirements).map { |req| DIETARY_REQUIREMENTS[req.to_sym] })
  }

  def supports_dietary_requirement?(requirement)
    return false if dietary_requirements.blank?
    requirement_value = DIETARY_REQUIREMENTS[requirement.to_sym]
    dietary_requirements.include?(requirement_value)
  end

  def can_be_made_for_dietary_requirement?(requirement)
    # Check if recipe natively supports it
    return true if supports_dietary_requirement?(requirement)

    # Check if all ingredients have substitutions or are already compliant
    recipe_ingredients.all? do |ri|
      ri.has_substitution_for?(requirement) ||
      ingredient_meets_requirement?(ri.ingredient, requirement)
    end
  end

  def ingredients_list_for_dietary_requirement(requirement = nil)
    recipe_ingredients.ordered.map do |ri|
      ri.full_description(requirement)
    end
  end

  def substitutions_needed_for(requirement)
    recipe_ingredients.joins(:substitutions)
                     .where(ingredient_substitutions: { dietary_requirement: requirement })
                     .includes(:substitutions)
  end

  def update_average_rating!
    if ratings.any?
      self.average_rating = ratings.average(:rating).round(2)
      self.rating_count = ratings.count
    else
      self.average_rating = nil
      self.rating_count = 0
    end
    save!
  end

  def user_comments(user)
    recipe_comments.find_by(user: user)
  end

  def user_rating(user)
    ratings.find_by(user: user)
  end

  def ingredients_with_details
    recipe_ingredients.includes(:ingredient, :unit).order(:display_order)
  end



  private

  def total_minutes_greater_equal_than_working_minutes
    if total_minutes.present? && working_minutes.present? && total_minutes < working_minutes
      errors.add(:total_minutes, "must be greater than or equal to working minutes")
    end
  end

  def ingredient_meets_requirement?(ingredient, requirement)
    # Since ingredients don't have dietary requirement flags in the current schema,
    # we assume all ingredients require substitutions for dietary requirements.
    # This ensures that dietary adaptations only work when explicit substitutions are provided.
    #
    # Future enhancement: Add dietary requirement boolean fields to ingredients table
    # (e.g., vegetarian, vegan, gluten_free, dairy_free, etc.) and implement logic here:
    #
    # case requirement.to_s
    # when "vegetarian"
    #   ingredient.vegetarian?
    # when "vegan"
    #   ingredient.vegan?
    # when "gluten_free"
    #   ingredient.gluten_free?
    # else
    #   false
    # end
    false
  end
end
