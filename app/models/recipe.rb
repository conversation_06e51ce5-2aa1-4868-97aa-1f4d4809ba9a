class Recipe < ApplicationRecord
  has_one_attached :image
  has_rich_text :description
  has_rich_text :instructions

  has_many :recipe_ingredients, dependent: :destroy
  has_many :ingredients, through: :recipe_ingredients

  # Recipe Variants System - Complete alternative versions for dietary requirements
  # Each variant can have different ingredients, cooking methods, timing, and instructions
  has_many :recipe_variants, dependent: :destroy

  # Dynamic Enum Associations
  belongs_to :cuisine, optional: true
  belongs_to :protein, optional: true
  has_many :recipe_tags, dependent: :destroy
  has_many :tags, through: :recipe_tags
  has_many :recipe_dietary_requirements, dependent: :destroy
  has_many :dietary_requirements, through: :recipe_dietary_requirements

  has_many :ratings, dependent: :destroy
  has_many :recipe_comments, dependent: :destroy
  has_many :users, through: :ratings

  # Helper methods for dynamic enums
  def tag_names
    tags.active.alphabetical.pluck(:name)
  end

  def dietary_requirement_names
    dietary_requirements.active.alphabetical.pluck(:name)
  end

  # Class methods for form helpers
  def self.cuisines_for_select
    Cuisine.active_alphabetical.pluck(:name, :id)
  end

  def self.proteins_for_select
    Protein.active_alphabetical.pluck(:name, :id)
  end

  def self.tags_for_select
    Tag.active_alphabetical.pluck(:name, :id)
  end

  def self.dietary_requirements_for_select
    DietaryRequirement.active_alphabetical.pluck(:name, :id)
  end

  validates :title, presence: true, length: { maximum: 50 }, uniqueness: true
  validates :sub_title, length: { maximum: 80 }
  validates :working_minutes, :total_minutes, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :suggested_serves, presence: true, numericality: { greater_than: 0 }, allow_nil: false
  validate :total_minutes_greater_equal_than_working_minutes

  scope :with_dietary_requirements, ->(requirements) {
    joins(:dietary_requirements).where(dietary_requirements: { name: requirements })
  }

  def supports_dietary_requirement?(requirement)
    dietary_requirements.active.exists?(name: requirement.to_s)
  end

  def can_be_made_for_dietary_requirement?(requirement)
    # Check if recipe natively supports it
    return true if supports_dietary_requirement?(requirement)

    # Check if there's a variant for this dietary requirement
    has_variant_for?(requirement)
  end

  def has_variant_for?(requirement)
    recipe_variants.joins(:dietary_requirements).exists?(dietary_requirements: { name: requirement.to_s })
  end

  def variant_for(requirement)
    recipe_variants.joins(:dietary_requirements).find_by(dietary_requirements: { name: requirement.to_s })
  end

  def ingredients_list_for_dietary_requirement(requirement = nil)
    if requirement && (variant = variant_for(requirement))
      variant.ingredients_list
    else
      recipe_ingredients.ordered.map(&:full_description)
    end
  end

  def available_dietary_requirements
    # Return dietary requirements that are either natively supported or have variants
    available = []

    # Add natively supported requirements
    available += dietary_requirements.active.pluck(:name)

    # Add requirements that have variants
    variant_requirements = recipe_variants.joins(:dietary_requirements)
                                         .includes(:dietary_requirements)
                                         .flat_map { |v| v.dietary_requirements.active.pluck(:name) }
    available += variant_requirements

    available.uniq
  end

  def update_average_rating!
    if ratings.any?
      self.average_rating = ratings.average(:rating).round(2)
      self.rating_count = ratings.count
    else
      self.average_rating = nil
      self.rating_count = 0
    end
    save!
  end

  def user_comments(user)
    recipe_comments.find_by(user: user)
  end

  def user_rating(user)
    ratings.find_by(user: user)
  end

  def ingredients_with_details
    recipe_ingredients.includes(:ingredient, :unit).order(:display_order)
  end



  private

  def total_minutes_greater_equal_than_working_minutes
    if total_minutes.present? && working_minutes.present? && total_minutes < working_minutes
      errors.add(:total_minutes, "must be greater than or equal to working minutes")
    end
  end

  # Legacy method - no longer used with variant system
  # Kept for backward compatibility
  def ingredient_meets_requirement?(ingredient, requirement)
    false
  end
end
