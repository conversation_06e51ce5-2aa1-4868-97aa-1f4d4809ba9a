class RecipeVariant < ApplicationRecord
  belongs_to :recipe
  has_many :variant_ingredients, dependent: :destroy
  has_many :ingredients, through: :variant_ingredients
  has_many :units, through: :variant_ingredients

  has_rich_text :instructions

  has_many :variant_dietary_requirements, dependent: :destroy
  has_many :dietary_requirements, through: :variant_dietary_requirements

  validates :recipe_id, presence: true
  validates :name, length: { maximum: 100 }
  validates :working_minutes, :total_minutes, numericality: { greater_than_or_equal_to: 0 }
  validate :total_minutes_greater_equal_than_working_minutes

  scope :ordered, -> { joins(:dietary_requirements).order("dietary_requirements.name") }

  def display_name
    if name.present?
      name
    elsif dietary_requirements.any?
      "#{dietary_requirements.first.name.humanize} Version"
    else
      "Recipe Variant"
    end
  end

  def primary_dietary_requirement
    dietary_requirements.first
  end

  def ingredients_with_details
    variant_ingredients.includes(:ingredient, :unit).order(:display_order)
  end

  def ingredients_list
    variant_ingredients.ordered.map(&:full_description)
  end

  private

  def total_minutes_greater_equal_than_working_minutes
    if total_minutes.present? && working_minutes.present? && total_minutes < working_minutes
      errors.add(:total_minutes, "must be greater than or equal to working minutes")
    end
  end
end
