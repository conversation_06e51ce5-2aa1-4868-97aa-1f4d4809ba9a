class RecipeVariant < ApplicationRecord
  belongs_to :recipe
  has_many :variant_ingredients, dependent: :destroy
  has_many :ingredients, through: :variant_ingredients
  has_many :units, through: :variant_ingredients

  enum :dietary_requirement, {
    vegetarian: 0,
    vegan: 1,
    gluten_free: 2,
    dairy_free: 3,
    keto: 4,
    paleo: 5,
    low_carb: 6,
    nut_free: 7,
    kosher: 8,
    halal: 9
  }

  validates :dietary_requirement, presence: true, uniqueness: { scope: :recipe_id }
  validates :name, length: { maximum: 100 }
  validates :working_minutes, :total_minutes, numericality: { greater_than_or_equal_to: 0 }
  validate :total_minutes_greater_equal_than_working_minutes

  scope :ordered, -> { order(:dietary_requirement) }

  def display_name
    name.present? ? name : "#{dietary_requirement.humanize} Version"
  end

  def ingredients_with_details
    variant_ingredients.includes(:ingredient, :unit).order(:display_order)
  end

  def ingredients_list
    variant_ingredients.ordered.map(&:full_description)
  end

  private

  def total_minutes_greater_equal_than_working_minutes
    if total_minutes.present? && working_minutes.present? && total_minutes < working_minutes
      errors.add(:total_minutes, "must be greater than or equal to working minutes")
    end
  end
end
