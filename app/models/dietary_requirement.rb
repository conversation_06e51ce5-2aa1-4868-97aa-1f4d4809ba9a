class DietaryRequirement < ApplicationRecord
  has_many :recipe_dietary_requirements, dependent: :destroy
  has_many :recipes, through: :recipe_dietary_requirements
  has_many :variant_dietary_requirements, dependent: :destroy
  has_many :recipe_variants, through: :variant_dietary_requirements

  validates :name, presence: true, uniqueness: { case_sensitive: false }, length: { maximum: 50 }
  validates :active, inclusion: { in: [ true, false ] }

  scope :active, -> { where(active: true) }
  scope :alphabetical, -> { order(:name) }
  scope :active_alphabetical, -> { active.alphabetical }

  before_validation :normalize_name

  def to_s
    name
  end

  private

  def normalize_name
    self.name = name&.strip&.downcase&.gsub(/[_\s]+/, " ")
  end
end
