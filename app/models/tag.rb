class Tag < ApplicationRecord
  has_many :recipe_tags, dependent: :destroy
  has_many :recipes, through: :recipe_tags

  validates :name, presence: true, uniqueness: { case_sensitive: false }, length: { maximum: 50 }
  validates :active, inclusion: { in: [ true, false ] }

  scope :active, -> { where(active: true) }
  scope :alphabetical, -> { order(:name) }
  scope :active_alphabetical, -> { active.alphabetical }

  before_validation :normalize_name

  def to_s
    name
  end

  private

  def normalize_name
    self.name = name&.strip
  end
end
