/*
 * This is a manifest file that'll be compiled into application.css.
 *
 * With Propshaft, assets are served efficiently without preprocessing steps. You can still include
 * application-wide styles in this file, but keep in mind that CSS precedence will follow the standard
 * cascading order, meaning styles declared later in the document or manifest will override earlier ones,
 * depending on specificity.
 *
 * Consider organizing styles into separate files for maintainability.
 */
 @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');
 
:root {
  --primary_colour: #4f785a;
  --background_colour: #f9f6f1;
  --text_colour: #2c2c2c;
  --secondary_colour: #e4572e;
  --neutral_colour: #e5e3dc;
  --highlight_colour: #f2b134;
}

 body {
  font-family: Nunito, Helvetica, sans-serif;
  padding: 1rem;
  background-color: var(--background_colour);
  color: var(--text_colour);
}

nav {
  justify-content: flex-end;
  display: flex;
  font-size: 0.875em;
  gap: 0.5rem;
  max-width: 1024px;
  margin: 0 auto;
  padding: 1rem;
}

nav a {
  display: inline-block;
}

main {
  max-width: 1024px;
  margin: 0 auto;
}

.button-primary {
  background-color: var(--primary_colour);
  color: var(--background_colour);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  margin: 0.5rem;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  text-decoration: none;
}

.button-primary:hover {
  background-color: var(--highlight_colour);
}

.button-secondary {
  background-color: var(--secondary_colour);
  color: var(--background_colour);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  margin: 0.5rem;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  text-decoration: none;
}

.button-secondary:hover {
  background-color: var(--highlight_colour);
}
