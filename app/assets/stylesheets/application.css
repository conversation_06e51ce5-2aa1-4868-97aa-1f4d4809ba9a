/*
 * This is a manifest file that'll be compiled into application.css.
 *
 * With Propshaft, assets are served efficiently without preprocessing steps. You can still include
 * application-wide styles in this file, but keep in mind that CSS precedence will follow the standard
 * cascading order, meaning styles declared later in the document or manifest will override earlier ones,
 * depending on specificity.
 *
 * Consider organizing styles into separate files for maintainability.
 */
 @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');
 
:root {
  --primary_colour: #4f785a;
  --background_colour: #f9f6f1;
  --text_colour: #2c2c2c;
  --secondary_colour: #e4572e;
  --neutral_colour: #e5e3dc;
  --highlight_colour: #f2b134;
}

 body {
  font-family: Nunito, Helvetica, sans-serif;
  padding: 1rem;
  background-color: var(--background_colour);
  color: var(--text_colour);
}

nav {
  justify-content: flex-end;
  display: flex;
  font-size: 0.875em;
  gap: 0.5rem;
  max-width: 1024px;
  margin: 0 auto;
  padding: 1rem;
}

nav a {
  display: inline-block;
}

main {
  max-width: 1024px;
  margin: 0 auto;
}

.button-primary {
  background-color: var(--primary_colour);
  color: var(--background_colour);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  margin: 0.5rem;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  text-decoration: none;
}

.button-primary:hover {
  background-color: var(--highlight_colour);
}

.button-secondary {
  background-color: var(--secondary_colour);
  color: var(--background_colour);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  margin: 0.5rem;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  text-decoration: none;
}

.button-secondary:hover {
  background-color: var(--highlight_colour);
}

/* Button styles for consistency */
.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  margin: 0.25rem;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  text-decoration: none;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: var(--primary_colour);
  color: var(--background_colour);
}

.btn-primary:hover {
  background-color: var(--highlight_colour);
}

.btn-secondary {
  background-color: var(--secondary_colour);
  color: var(--background_colour);
}

.btn-secondary:hover {
  background-color: var(--highlight_colour);
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

/* Recipe grid styles */
.recipes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.recipes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.recipe-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.2s;
}

.recipe-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.recipe-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.recipe-card-content {
  padding: 1rem;
}

.recipe-card h3 {
  margin: 0 0 0.5rem 0;
}

.recipe-card h3 a {
  text-decoration: none;
  color: var(--text_colour);
}

.recipe-subtitle {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.recipe-meta {
  display: flex;
  gap: 1rem;
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #666;
}

.recipe-tags, .dietary-info {
  display: flex;
  gap: 0.25rem;
  margin: 0.5rem 0;
}

.tag, .dietary-tag {
  background: var(--neutral_colour);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
}

.dietary-tag {
  background: var(--primary_colour);
  color: white;
}

/* Recipe detail styles */
.recipe-header {
  text-align: center;
  margin-bottom: 2rem;
}

.recipe-content {
  max-width: 800px;
  margin: 0 auto;
}

.recipe-image img {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.recipe-meta {
  background: white;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.recipe-ingredients, .recipe-rating, .recipe-comments {
  background: white;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.recipe-ingredients ul {
  list-style: none;
  padding: 0;
}

.recipe-ingredients li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.small-link {
  font-size: 0.8rem;
  margin-left: 0.5rem;
}

.text-danger {
  color: #dc3545;
}

.comment {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
}

/* Form styles */
.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: bold;
}

.field input, .field select, .field textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  font-size: 1rem;
}

.actions {
  margin-top: 1rem;
}

/* Ingredient and Unit list styles */
.ingredients-list, .units-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.ingredient-card, .unit-card {
  background: white;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ingredient-card h3, .unit-card h4 {
  margin: 0 0 0.5rem 0;
}

.ingredient-card h3 a, .unit-card h4 a {
  text-decoration: none;
  color: var(--text_colour);
}

.actions {
  margin-top: 0.5rem;
}

.actions a {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #666;
}

/* Error messages */
#error_explanation {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
}

#error_explanation h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

#error_explanation ul {
  margin: 0;
  padding-left: 1.5rem;
}
