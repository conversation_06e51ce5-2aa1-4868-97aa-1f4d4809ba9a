/*
 * This is a manifest file that'll be compiled into application.css.
 *
 * With Propshaft, assets are served efficiently without preprocessing steps. You can still include
 * application-wide styles in this file, but keep in mind that CSS precedence will follow the standard
 * cascading order, meaning styles declared later in the document or manifest will override earlier ones,
 * depending on specificity.
 *
 * Consider organizing styles into separate files for maintainability.
 */
 @import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');
 
:root {
  /* Brand Colors */
  --primary_colour: #4f785a;
  --background_colour: #f9f6f1;
  --text_colour: #2c2c2c;
  --secondary_colour: #e4572e;
  --neutral_colour: #e5e3dc;
  --highlight_colour: #f2b134;

  /* Extended Brand Palette */
  --white: #ffffff;
  --light_grey: #f8f9fa;
  --medium_grey: #e9ecef;
  --border_grey: #dee2e6;
  --text_muted: #6c757d;
  --text_light: #495057;

  /* Semantic Colors (using brand-friendly alternatives) */
  --success_colour: #4f785a; /* Using your primary green for success */
  --warning_colour: #f2b134; /* Using your highlight yellow for warnings */
  --danger_colour: #e4572e; /* Using your secondary orange for danger */
  --info_colour: #4f785a; /* Using your primary for info */

  /* Interactive States */
  --primary_hover: #3e6147; /* Darker shade of primary */
  --secondary_hover: #d1471f; /* Darker shade of secondary */
  --danger_hover: #d1471f; /* Consistent with secondary hover */
}

*, *::before, *::after {
  box-sizing: border-box;
}

body {
  font-family: Nunito, Helvetica, sans-serif;
  background-color: var(--background_colour);
  color: var(--text_colour);
  margin: 0;
  font-size: 1.25rem;
}

nav {
  justify-content: flex-end;
  display: flex;
  font-size: 0.875em;
  gap: 0.5rem;
  margin: 0;
  background-color: var(--neutral_colour);
  padding: 0.5rem 3rem;
}

.nav-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 0 auto;
}

.nav-links {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.nav-links a {
  color: var(--text_colour);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
  font-weight: bold;
}

.nav-links a:hover {
  background-color: var(--light_grey);
  color: var(--primary_colour);
}

.logo {
  height: 3rem;
}

main {
  padding: 2rem 3rem;
}

/* Button styles for consistency */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  margin: 0.25rem;
  border: 1px solid transparent;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
  font-family: Nunito, Helvetica, sans-serif;
  text-align: center;
}

/* Ensure button_to forms display inline */
form.button_to {
  display: inline;
}

form.button_to input[type="submit"] {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  margin: 0.25rem;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  text-decoration: none;
  transition: background-color 0.2s;
}

form.button_to input[type="submit"].btn-danger {
  background-color: var(--danger_colour);
  color: var(--white);
}

form.button_to input[type="submit"].btn-danger:hover {
  background-color: var(--danger_hover);
}

.btn-primary {
  background-color: var(--primary_colour);
  color: var(--background_colour);
}

.btn-primary:hover {
  background-color: var(--highlight_colour);
}

.btn-danger {
  background-color: var(--danger_colour);
  color: var(--white);
}

.btn-danger:hover {
  background-color: var(--danger_hover);
}

/* Recipe grid styles */
.recipes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.recipes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.recipe-card {
  background: var(--white);
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.2s;
}

.recipe-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.recipe-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.recipe-card-content {
  padding: 1rem;
}

.recipe-card h3 {
  margin: 0 0 0.5rem 0;
}

.recipe-card h3 a {
  text-decoration: none;
  color: var(--text_colour);
}

.recipe-subtitle {
  color: var(--text_muted);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.recipe-meta {
  display: flex;
  gap: 1rem;
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text_muted);
}

.recipe-tags, .dietary-info {
  display: flex;
  gap: 0.25rem;
  margin: 0.5rem 0;
}

.tag, .dietary-tag {
  background: var(--neutral_colour);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
}

.dietary-tag {
  background: var(--primary_colour);
  color: var(--white);
}

/* Recipe detail styles */
.recipe-header {
  text-align: center;
  margin-bottom: 2rem;
}

.recipe-content {
  max-width: 800px;
  margin: 0 auto;
}

.recipe-image img {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.recipe-meta {
  background: var(--white);
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.recipe-ingredients, .recipe-rating, .recipe-comments {
  background: var(--white);
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.recipe-ingredients ul {
  list-style: none;
  padding: 0;
}

.recipe-ingredients li {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border_grey);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.small-link {
  font-size: 0.8rem;
  margin-left: 0.5rem;
}

.text-danger {
  color: var(--danger_colour);
}

.comment {
  background: var(--light_grey);
  padding: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
}

/* Form styles */
.field {
  margin-bottom: 1rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text_colour);
  font-family: Nunito, Helvetica, sans-serif;
}

.field input, .field select, .field textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border_grey);
  border-radius: 0.5rem;
  font-size: 1rem;
  font-family: Nunito, Helvetica, sans-serif;
  background-color: var(--white);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.field input:focus, .field select:focus, .field textarea:focus {
  outline: none;
  border-color: var(--primary_colour);
  box-shadow: 0 0 0 3px rgba(79, 120, 90, 0.1);
}

.actions {
  margin-top: 1rem;
}

/* Ingredient and Unit list styles */
.ingredients-list, .units-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.ingredient-card, .unit-card {
  background: var(--white);
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ingredient-card h3, .unit-card h4 {
  margin: 0 0 0.5rem 0;
}

.ingredient-card h3 a, .unit-card h4 a {
  text-decoration: none;
  color: var(--text_colour);
}

.actions {
  margin-top: 0.5rem;
}

.actions a {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

/* Error messages */
#error_explanation {
  background: var(--light_grey);
  border: 1px solid var(--danger_colour);
  color: var(--danger_colour);
  padding: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
}

#error_explanation h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

#error_explanation ul {
  margin: 0;
  padding-left: 1.5rem;
}

/* Form sections */
.form-section {
  background: var(--white);
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  max-width: 1000px;
}

.form-section h3 {
  margin: 0 0 1rem 0;
  color: var(--primary_colour);
  border-bottom: 2px solid var(--neutral_colour);
  padding-bottom: 0.5rem;
}

/* Checkbox groups */
.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.checkbox-label:hover {
  background-color: var(--light_grey);
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* Dietary requirements functionality */
.dietary-filter {
  background: var(--white);
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dietary-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.btn-disabled {
  background-color: var(--medium_grey);
  color: var(--text_muted);
  cursor: not-allowed;
  opacity: 0.6;
}

.dietary-badge {
  background: var(--primary_colour);
  color: var(--white);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
  font-weight: normal;
}

.ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.ingredient-description {
  flex: 1;
}

.ingredient-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.substitution-indicator {
  font-size: 1.2rem;
  cursor: help;
}

.substitutions-list {
  background: var(--light_grey);
  padding: 0.75rem;
  border-radius: 0.25rem;
  margin-top: 0.5rem;
  margin-left: 1rem;
}

.substitutions-list h5 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--text_muted);
}

.substitution-item {
  background: var(--white);
  padding: 0.5rem;
  border-radius: 0.25rem;
  margin-bottom: 0.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.substitution-item:last-child {
  margin-bottom: 0;
}

/* Flash messages */
.notice, .alert {
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0.25rem;
}

.notice {
  background-color: var(--light_grey);
  color: var(--success_colour);
  border: 1px solid var(--success_colour);
}

.alert {
  background-color: var(--light_grey);
  color: var(--danger_colour);
  border: 1px solid var(--danger_colour);
}

/* Recipe Variants */
.variant-details {
  background-color: var(--light_grey);
  border: 1px solid var(--border_grey);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.variant-timing {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
}

.variant-timing p {
  margin: 0;
  padding: 0.5rem 1rem;
  background-color: var(--medium_grey);
  border-radius: 0.25rem;
}

.variant-instructions, .variant-notes {
  margin: 1rem 0;
}

.variant-instructions h4, .variant-notes h4 {
  margin-bottom: 0.5rem;
  color: var(--text_light);
}

.instructions-content, .notes-content {
  background-color: var(--white);
  padding: 1rem;
  border-radius: 0.25rem;
  border: 1px solid var(--border_grey);
}

.variant-actions {
  margin-top: 1.5rem;
  display: flex;
  gap: 1rem;
}

.btn-outline {
  background-color: transparent;
  color: var(--primary_colour);
  border: 1px solid var(--primary_colour);
}

.btn-outline:hover {
  background-color: var(--primary_colour);
  color: var(--white);
}

/* Centered Container for Forms and Admin */
.centered-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

/* Authentication Pages */
.auth-container {
  max-width: 400px;
  margin: 3rem auto;
  padding: 0;
}

/* Header styling for forms and admin pages */
.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  color: var(--primary_colour);
  margin-bottom: 0.5rem;
  font-size: 2rem;
  font-weight: 700;
}

.header p {
  color: var(--text_muted);
  margin: 0;
  font-size: 1.1rem;
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-header h1 {
  color: var(--primary_colour);
  margin-bottom: 0.5rem;
}

.auth-header p {
  color: var(--text_muted);
  margin: 0;
}

.auth-links {
  text-align: center;
  margin-top: 1.5rem;
}

/* Card Layouts for Ingredients and Units */
.ingredients-list, .units-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.ingredient-card, .unit-card {
  background-color: var(--white);
  border: 1px solid var(--border_grey);
  border-radius: 0.5rem;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ingredient-card:hover, .unit-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-content {
  flex: 1;
}

.card-content h3 {
  margin: 0 0 0.5rem 0;
  color: var(--primary_colour);
  font-size: 1.25rem;
  font-weight: 600;
}

.card-content .description {
  color: var(--text_muted);
  margin: 0;
  line-height: 1.4;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.unit-details {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.unit-details span {
  font-size: 0.9rem;
}

.universal-badge {
  background-color: var(--primary_colour);
  color: var(--white);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
  font-weight: 600;
}

.units-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.unit-section {
  background-color: var(--white);
  border: 1px solid var(--border_grey);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.unit-section h2 {
  margin: 0 0 1.5rem 0;
  color: var(--primary_colour);
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid var(--primary_colour);
  padding-bottom: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  background-color: var(--light_grey);
  border-radius: 0.5rem;
  border: 1px solid var(--border_grey);
}

.empty-state h3 {
  color: var(--text_colour);
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: var(--text_muted);
  margin-bottom: 1.5rem;
}

.recipe-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.recipe-link {
  display: inline-block;
}

/* Unit Conversion Display */
.ingredient-description .text-muted.small {
  font-size: 0.8rem;
  margin-left: 0.5rem;
  opacity: 0.7;
}

/* Recipe Instructions */
.recipe-instructions {
  background-color: var(--light_grey);
  border: 1px solid var(--border_grey);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.recipe-instructions h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text_light);
}

.instructions-content {
  background-color: var(--white);
  padding: 1rem;
  border-radius: 0.25rem;
  border: 1px solid var(--border_grey);
  line-height: 1.6;
}

/* Rich text editor styling */
.instructions-content h1,
.instructions-content h2,
.instructions-content h3 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}

.instructions-content h1:first-child,
.instructions-content h2:first-child,
.instructions-content h3:first-child {
  margin-top: 0;
}

.instructions-content ul,
.instructions-content ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.instructions-content li {
  margin-bottom: 0.5rem;
}

.instructions-content p {
  margin-bottom: 1rem;
}

.instructions-content strong {
  font-weight: 600;
}

/* Admin Interface */
.admin-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.admin-section {
  background-color: var(--white);
  border: 1px solid var(--border_grey);
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  cursor: pointer;
  background-color: var(--light_grey);
  border-radius: 0.5rem 0.5rem 0 0;
  transition: background-color 0.2s ease;
}

.section-header:hover {
  background-color: var(--border_grey);
}

.section-header h2 {
  margin: 0;
  color: var(--primary_colour);
  font-size: 1.25rem;
  font-weight: 600;
}

.toggle-icon {
  font-size: 1.2rem;
  color: var(--primary_colour);
  font-weight: bold;
  transition: transform 0.2s ease;
}

.section-content {
  padding: 1.5rem;
  border-top: 1px solid var(--border_grey);
}

.unit-subsection {
  margin-bottom: 2rem;
}

.unit-subsection:last-child {
  margin-bottom: 0;
}

.unit-subsection h3 {
  margin: 0 0 1rem 0;
  color: var(--text_colour);
  font-size: 1.1rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border_grey);
}

.admin-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.enum-section {
  background-color: var(--white);
  border: 1px solid var(--border_grey);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.enum-section h2 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: var(--primary_colour);
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid var(--primary_colour);
  padding-bottom: 0.5rem;
}

.add-form {
  background-color: var(--light_grey);
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  gap: 1rem;
  align-items: center;
  border: 1px solid var(--border_grey);
}

.add-form input[type="text"] {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--border_grey);
  border-radius: 0.5rem;
  font-family: Nunito, Helvetica, sans-serif;
  background-color: var(--white);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.add-form input[type="text"]:focus {
  outline: none;
  border-color: var(--primary_colour);
  box-shadow: 0 0 0 3px rgba(79, 120, 90, 0.1);
}

.enum-list {
  display: grid;
  gap: 0.5rem;
}

.enum-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.enum-item {
  display: grid;
  grid-template-columns: 200px 1fr 80px 200px;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  border: 1px solid var(--border_grey);
  border-radius: 0.5rem;
  background-color: var(--white);
  transition: all 0.2s ease;
}

.enum-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.enum-item.inactive {
  background-color: var(--light_grey);
  opacity: 0.7;
}

.enum-item .name {
  font-weight: 600;
  color: var(--text_colour);
  font-size: 1rem;
}

.enum-item .description {
  color: var(--text_muted);
  font-size: 0.9rem;
  line-height: 1.4;
}

.enum-item .status.active {
  color: var(--success_colour);
  font-weight: 600;
  font-size: 0.9rem;
}

.enum-item .status.inactive {
  color: var(--danger_colour);
  font-weight: 600;
  font-size: 0.9rem;
}

.enum-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.enum-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.enum-actions form {
  margin: 0;
  display: inline;
}



/* Checkbox controls */
.checkbox-controls {
  vertical-align: middle;
}

.checkbox-field {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-field input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary_colour);
  cursor: pointer;
}

.btn-secondary {
  background-color: var(--text_muted);
  color: var(--white);
  border: 1px solid var(--text_muted);
}

.btn-secondary:hover {
  background-color: var(--text_light);
  border-color: var(--text_light);
}
