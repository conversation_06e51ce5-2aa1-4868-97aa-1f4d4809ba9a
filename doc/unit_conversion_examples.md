# Unit Conversion System Examples

## How the Unit Conversion System Works

The unit conversion system automatically converts recipe ingredients to match the user's preferred unit system (metric or imperial) while preserving universal units like teaspoons and tablespoons.

## Unit Categories

### Universal Units (No Conversion)
- teaspoon (tsp)
- tablespoon (tbsp) 
- piece (pc)
- clove
- pinch

### Metric System
- **Weight**: gram (g), kilogram (kg)
- **Volume**: milliliter (ml), liter (l), cup (metric)
- **Temperature**: Celsius (C)

### Imperial System
- **Weight**: ounce (oz), pound (lb)
- **Volume**: fluid ounce (fl oz), cup (US), pint (pt), quart (qt), gallon (gal)
- **Temperature**: Fahrenheit (F)

## Conversion Examples

### Weight Conversions
- 100g → 3.53 oz
- 1 kg → 2.2 lb (approx)
- 8 oz → 227g

### Volume Conversions
- 250ml → 8.45 fl oz
- 1 liter → 33.8 fl oz
- 1 cup (US) → 237ml

### Temperature Conversions
- 180°C → 356°F
- 350°F → 177°C

### Universal Units (No Conversion)
- 2 tsp salt → 2 tsp salt (unchanged)
- 1 tbsp olive oil → 1 tbsp olive oil (unchanged)
- 3 cloves garlic → 3 cloves garlic (unchanged)

## User Experience

1. **User sets preference**: Metric or Imperial in user preferences
2. **Recipe display**: Ingredients automatically show in preferred units
3. **Original reference**: Converted amounts show original values in small text
4. **Universal preservation**: Teaspoons, tablespoons, etc. remain unchanged

## Example Recipe Display

**For a Metric User viewing an Imperial recipe:**
- Original: "2 cups flour"
- Displayed: "473 ml flour [orig: 2 cup_us]"

**For an Imperial User viewing a Metric recipe:**
- Original: "500g flour" 
- Displayed: "17.6 oz flour [orig: 500 g]"

**Universal units (same for both):**
- "2 tsp vanilla extract" → "2 tsp vanilla extract" (no conversion)
