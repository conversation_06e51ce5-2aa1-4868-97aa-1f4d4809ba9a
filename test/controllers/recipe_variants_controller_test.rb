require "test_helper"

class RecipeVariantsControllerTest < ActionDispatch::IntegrationTest
  test "should get new" do
    get recipe_variants_new_url
    assert_response :success
  end

  test "should get create" do
    get recipe_variants_create_url
    assert_response :success
  end

  test "should get edit" do
    get recipe_variants_edit_url
    assert_response :success
  end

  test "should get update" do
    get recipe_variants_update_url
    assert_response :success
  end

  test "should get destroy" do
    get recipe_variants_destroy_url
    assert_response :success
  end
end
