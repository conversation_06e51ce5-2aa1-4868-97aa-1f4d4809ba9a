require "test_helper"

class VariantIngredientsControllerTest < ActionDispatch::IntegrationTest
  test "should get new" do
    get variant_ingredients_new_url
    assert_response :success
  end

  test "should get create" do
    get variant_ingredients_create_url
    assert_response :success
  end

  test "should get edit" do
    get variant_ingredients_edit_url
    assert_response :success
  end

  test "should get update" do
    get variant_ingredients_update_url
    assert_response :success
  end

  test "should get destroy" do
    get variant_ingredients_destroy_url
    assert_response :success
  end
end
