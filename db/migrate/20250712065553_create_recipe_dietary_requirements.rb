class CreateRecipeDietaryRequirements < ActiveRecord::Migration[8.0]
  def change
    create_table :recipe_dietary_requirements do |t|
      t.references :recipe, null: false, foreign_key: true
      t.references :dietary_requirement, null: false, foreign_key: true

      t.timestamps
    end

    add_index :recipe_dietary_requirements, [ :recipe_id, :dietary_requirement_id ], unique: true, name: 'index_recipe_dietary_requirements_unique'
  end
end
