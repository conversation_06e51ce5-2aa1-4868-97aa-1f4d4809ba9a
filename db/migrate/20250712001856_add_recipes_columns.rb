class AddRecipesColumns < ActiveRecord::Migration[8.0]
  def change
    change_column :recipes, :title, :string, limit: 50
    add_column :recipes, :average_rating, :decimal, precision: 3, scale: 2
    add_column :recipes, :rating_count, :integer, null: false, default: 0
    add_column :recipes, :sub_title, :string, limit: 80
    add_column :recipes, :cuisine, :integer
    add_column :recipes, :protein, :integer
    add_column :recipes, :working_minutes, :integer, null: false, default: 0
    add_column :recipes, :total_minutes, :integer, null: false, default: 0
    add_column :recipes, :tags, :integer, array: true, default: []
    add_column :recipes, :dietary_requirements, :integer, array: true, default: []
    add_column :recipes, :suggested_serves, :integer, null: false, default: 1

    add_index :recipes, :title, unique: true
    add_index :recipes, :tags, using: :gin
    add_index :recipes, :cuisine
    add_index :recipes, :protein
    add_index :recipes, :average_rating
    add_index :recipes, :dietary_requirements, using: :gin

    add_check_constraint :recipes, "working_minutes >= 0", name: "working_minutes_positive_check"
    add_check_constraint :recipes, "total_minutes >= 0", name: "total_minutes_positive_check"
    add_check_constraint :recipes, "suggested_serves > 0", name: "suggested_serves_positive_check"
  end
end
