class AddMissingIndexes < ActiveRecord::Migration[8.0]
  def change
    # Add index on recipes.created_at for chronological queries
    add_index :recipes, :created_at

    # Add index on sessions.created_at for session cleanup
    add_index :sessions, :created_at

    # Add composite index on recipe_ingredients for uniqueness checks
    add_index :recipe_ingredients, [ :recipe_id, :ingredient_id ], unique: true
  end
end
