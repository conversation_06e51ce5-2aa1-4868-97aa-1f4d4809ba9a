class AddDataIntegrityConstraints < ActiveRecord::Migration[8.0]
  def change
    # Ensure rating averages are within valid range
    add_check_constraint :recipes, "average_rating >= 1.0 AND average_rating <= 5.0", name: "average_rating_range_check"

    # Ensure arrays aren't empty when present (but can be empty arrays)
    add_check_constraint :recipes, "array_length(tags, 1) > 0 OR tags = '{}'", name: "tags_not_null_array_check"
    add_check_constraint :recipes, "array_length(dietary_requirements, 1) > 0 OR dietary_requirements = '{}'", name: "dietary_requirements_not_null_array_check"

    # Ensure recipe titles are not just whitespace
    add_check_constraint :recipes, "trim(title) != ''", name: "title_not_blank_check"

    # Ensure ingredient names are not just whitespace
    add_check_constraint :ingredients, "trim(name) != ''", name: "ingredient_name_not_blank_check"

    # Ensure unit names and abbreviations are not just whitespace
    add_check_constraint :units, "trim(name) != ''", name: "unit_name_not_blank_check"
    add_check_constraint :units, "trim(abbreviation) != ''", name: "unit_abbreviation_not_blank_check"
  end
end
