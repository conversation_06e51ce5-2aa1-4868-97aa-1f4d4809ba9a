class CreateRecipeVariants < ActiveRecord::Migration[8.0]
  def change
    create_table :recipe_variants do |t|
      t.references :recipe, null: false, foreign_key: true
      t.integer :dietary_requirement, null: false
      t.string :name, limit: 100
      t.text :instructions
      t.integer :working_minutes, default: 0
      t.integer :total_minutes, default: 0
      t.text :notes

      t.timestamps
    end

    add_index :recipe_variants, [ :recipe_id, :dietary_requirement ], unique: true
    add_index :recipe_variants, :dietary_requirement

    add_check_constraint :recipe_variants, "working_minutes >= 0", name: "working_minutes_positive_check"
    add_check_constraint :recipe_variants, "total_minutes >= 0", name: "total_minutes_positive_check"
    add_check_constraint :recipe_variants, "total_minutes >= working_minutes", name: "total_minutes_gte_working_minutes_check"
  end
end
