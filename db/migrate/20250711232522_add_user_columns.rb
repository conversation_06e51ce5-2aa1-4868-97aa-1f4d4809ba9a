class AddUserColumns < ActiveRecord::Migration[8.0]
  def up
    add_column :users, :username, :string
    add_column :users, :admin, :boolean, null: false, default: false
    add_column :users, :subscription_level, :integer, null: false, default: 0

    execute <<-SQL.squish
      UPDATE users SET username = email_address WHERE username IS NULL;
    SQL

    change_column_null :users, :username, false
  end

  def down
    change_column_null :users, :username, true
    remove_column :users, :username
    remove_column :users, :admin
    remove_column :users, :subscription_level
  end
end
