class CreateVariantDietaryRequirements < ActiveRecord::Migration[8.0]
  def change
    create_table :variant_dietary_requirements do |t|
      t.references :recipe_variant, null: false, foreign_key: true
      t.references :dietary_requirement, null: false, foreign_key: true

      t.timestamps
    end

    add_index :variant_dietary_requirements, [ :recipe_variant_id, :dietary_requirement_id ], unique: true, name: 'index_variant_dietary_requirements_unique'
  end
end
