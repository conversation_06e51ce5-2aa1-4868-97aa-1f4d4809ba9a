class RemoveUnusedConstraints < ActiveRecord::Migration[8.0]
  def change
    # Remove constraints that were for old enum arrays (no longer exist)
    # These constraints were added in 20250712041716_add_data_integrity_constraints.rb
    # but the columns they reference were removed in 20250712071028_update_recipes_to_use_dynamic_enums.rb

    # Note: These constraints were already removed when the columns were dropped,
    # but this migration documents the cleanup for clarity

    # The following constraints were automatically removed when columns were dropped:
    # - tags_not_null_array_check (for recipes.tags column)
    # - dietary_requirements_not_null_array_check (for recipes.dietary_requirements column)
  end
end
