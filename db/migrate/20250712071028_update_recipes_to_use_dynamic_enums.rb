class UpdateRecipesToUseDynamicEnums < ActiveRecord::Migration[8.0]
  def change
    # Add foreign key columns for dynamic enums
    add_reference :recipes, :cuisine, null: true, foreign_key: true
    add_reference :recipes, :protein, null: true, foreign_key: true

    # Remove old enum columns
    remove_column :recipes, :cuisine, :integer
    remove_column :recipes, :protein, :integer
    remove_column :recipes, :tags, :integer, array: true
    remove_column :recipes, :dietary_requirements, :integer, array: true
  end
end
