class CreateVariantIngredients < ActiveRecord::Migration[8.0]
  def change
    create_table :variant_ingredients do |t|
      t.references :recipe_variant, null: false, foreign_key: true
      t.references :ingredient, null: false, foreign_key: true
      t.references :unit, null: false, foreign_key: true
      t.decimal :amount, precision: 12, scale: 4, null: false
      t.integer :preparation
      t.text :notes
      t.integer :display_order, null: false

      t.timestamps
    end

    add_index :variant_ingredients, [ :recipe_variant_id, :display_order ]

    add_check_constraint :variant_ingredients, "amount > 0", name: "variant_amount_positive_check"
    add_check_constraint :variant_ingredients, "display_order > 0", name: "variant_display_order_positive_check"
  end
end
