class CreateUnitsTable < ActiveRecord::Migration[8.0]
  def change
    create_table :units do |t|
      t.string :name, null: false, limit: 25
      t.string :abbreviation, null: false, limit: 8
      t.integer :unit_type, null: false
      t.decimal :measure, precision: 12, scale: 4, null: false

      t.timestamps
    end

    add_index :units, :name, unique: true
    add_index :units, :abbreviation, unique: true

    add_check_constraint :units, "measure > 0", name: "measure_positive_check"
  end
end
