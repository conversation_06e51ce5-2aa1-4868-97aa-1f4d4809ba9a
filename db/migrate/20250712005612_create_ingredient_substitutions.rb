class CreateIngredientSubstitutions < ActiveRecord::Migration[8.0]
  def change
    create_table :ingredient_substitutions do |t|
      t.references :recipe_ingredient, null: false, foreign_key: true
      t.references :substitute_ingredient, null: false, foreign_key: { to_table: :ingredients }
      t.references :substitute_unit, null: false, foreign_key: { to_table: :units }
      t.decimal :substitute_amount, precision: 12, scale: 4, null: false
      t.integer :substitute_preperation
      t.integer :dietary_requirement, null: false
      t.text :substitute_notes

      t.timestamps
    end

    add_index :ingredient_substitutions, [ :recipe_ingredient_id, :dietary_requirement ], unique: true, name: "idx_substitutions_unique"
    add_check_constraint :ingredient_substitutions, "substitute_amount > 0", name: "substitute_amount_positive_check"
  end
end
