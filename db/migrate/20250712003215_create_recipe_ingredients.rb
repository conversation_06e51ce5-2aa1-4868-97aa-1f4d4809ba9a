class CreateRecipeIngredients < ActiveRecord::Migration[8.0]
  def change
    create_table :recipe_ingredients do |t|
      t.references :recipe, null: false, foreign_key: true
      t.references :ingredient, null: false, foreign_key: true
      t.decimal :amount, precision: 12, scale: 4, null: false
      t.references :unit, null: false, foreign_key: true
      t.integer :preperation
      t.text :notes
      t.integer :display_order, null: false, default: 1
      t.timestamps
    end

    add_index :recipe_ingredients, [ :recipe_id, :display_order ]

    add_check_constraint :recipe_ingredients, "amount > 0", name: "amount_positive_check"
    add_check_constraint :recipe_ingredients, "display_order > 0", name: "display_order_positive_check"
  end
end
