class CreateRatings < ActiveRecord::Migration[8.0]
  def change
    create_table :ratings do |t|
      t.references :user, null: false, foreign_key: true
      t.references :recipe, null: false, foreign_key: true
      t.integer :rating, null: false

      t.timestamps
    end

    add_index :ratings, [ :user_id, :recipe_id ], unique: true

    add_check_constraint :ratings, "rating >= 1 AND rating <= 5", name: "rating_range_check"
  end
end
