# Clear existing units to start fresh
Unit.destroy_all

# Universal units (work in both systems)
universal_units = [
  { name: 'teaspoon', abbreviation: 'tsp', unit_type: 'volume', measure: 5.0, universal: true },
  { name: 'tablespoon', abbreviation: 'tbsp', unit_type: 'volume', measure: 15.0, universal: true },
  { name: 'piece', abbreviation: 'pc', unit_type: 'volume', measure: 1.0, universal: true },
  { name: 'clove', abbreviation: 'clove', unit_type: 'volume', measure: 1.0, universal: true },
  { name: 'pinch', abbreviation: 'pinch', unit_type: 'volume', measure: 0.5, universal: true }
]

# Metric units (measure = conversion to base metric unit)
metric_units = [
  # Weight (base: grams)
  { name: 'gram', abbreviation: 'g', unit_type: 'weight', measure: 1.0, unit_system: 'metric' },
  { name: 'kilogram', abbreviation: 'kg', unit_type: 'weight', measure: 1000.0, unit_system: 'metric' },

  # Volume (base: milliliters)
  { name: 'milliliter', abbreviation: 'ml', unit_type: 'volume', measure: 1.0, unit_system: 'metric' },
  { name: 'liter', abbreviation: 'l', unit_type: 'volume', measure: 1000.0, unit_system: 'metric' },
  { name: 'cup (metric)', abbreviation: 'cup_m', unit_type: 'volume', measure: 250.0, unit_system: 'metric' },

  # Temperature (base: Celsius)
  { name: 'Celsius', abbreviation: 'C', unit_type: 'temperature', measure: 1.0, unit_system: 'metric' }
]

# Imperial units (measure = conversion to base imperial unit)
imperial_units = [
  # Weight (base: ounces)
  { name: 'ounce', abbreviation: 'oz', unit_type: 'weight', measure: 1.0, unit_system: 'imperial' },
  { name: 'pound', abbreviation: 'lb', unit_type: 'weight', measure: 16.0, unit_system: 'imperial' },

  # Volume (base: fluid ounces)
  { name: 'fluid ounce', abbreviation: 'fl oz', unit_type: 'volume', measure: 1.0, unit_system: 'imperial' },
  { name: 'cup (US)', abbreviation: 'cup_us', unit_type: 'volume', measure: 8.0, unit_system: 'imperial' },
  { name: 'pint', abbreviation: 'pt', unit_type: 'volume', measure: 16.0, unit_system: 'imperial' },
  { name: 'quart', abbreviation: 'qt', unit_type: 'volume', measure: 32.0, unit_system: 'imperial' },
  { name: 'gallon', abbreviation: 'gal', unit_type: 'volume', measure: 128.0, unit_system: 'imperial' },

  # Temperature (base: Fahrenheit)
  { name: 'Fahrenheit', abbreviation: 'F', unit_type: 'temperature', measure: 1.0, unit_system: 'imperial' }
]

# Create all units
(universal_units + metric_units + imperial_units).each do |unit_data|
  Unit.find_or_create_by(name: unit_data[:name]) do |unit|
    unit.assign_attributes(unit_data)
  end
end

puts "Created #{Unit.count} units"
puts "Universal units: #{Unit.where(universal: true).count}"
puts "Metric units: #{Unit.where(unit_system: 'metric').count}"
puts "Imperial units: #{Unit.where(unit_system: 'imperial').count}"
