# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "Seeding dynamic enum data..."

# Cuisines
cuisines = [
  { name: "African", description: "Traditional African cuisine" },
  { name: "American", description: "Classic American dishes" },
  { name: "Asian", description: "Various Asian cuisines" },
  { name: "British", description: "Traditional British cuisine" },
  { name: "Caribbean", description: "Caribbean island cuisine" },
  { name: "Chinese", description: "Traditional Chinese cuisine" },
  { name: "European", description: "Various European cuisines" },
  { name: "French", description: "Classic French cuisine" },
  { name: "German", description: "Traditional German cuisine" },
  { name: "Greek", description: "Traditional Greek cuisine" },
  { name: "Indian", description: "Traditional Indian cuisine" },
  { name: "Italian", description: "Classic Italian cuisine" },
  { name: "Japanese", description: "Traditional Japanese cuisine" },
  { name: "Korean", description: "Traditional Korean cuisine" },
  { name: "Latin American", description: "Latin American cuisines" },
  { name: "Mediterranean", description: "Mediterranean cuisine" },
  { name: "Mexican", description: "Traditional Mexican cuisine" },
  { name: "Middle Eastern", description: "Middle Eastern cuisine" },
  { name: "Moroccan", description: "Traditional Moroccan cuisine" },
  { name: "Spanish", description: "Traditional Spanish cuisine" },
  { name: "Thai", description: "Traditional Thai cuisine" },
  { name: "Vietnamese", description: "Traditional Vietnamese cuisine" }
]

cuisines.each do |cuisine_data|
  Cuisine.find_or_create_by!(name: cuisine_data[:name]) do |cuisine|
    cuisine.description = cuisine_data[:description]
    cuisine.active = true
  end
end

# Proteins
proteins = [
  { name: "Beef", description: "Beef-based dishes" },
  { name: "Chicken", description: "Chicken-based dishes" },
  { name: "Dairy", description: "Dairy-based protein" },
  { name: "Duck", description: "Duck-based dishes" },
  { name: "Eggs", description: "Egg-based protein" },
  { name: "Fish", description: "Fish-based dishes" },
  { name: "Lamb", description: "Lamb-based dishes" },
  { name: "Legumes", description: "Bean and legume protein" },
  { name: "Nuts", description: "Nut-based protein" },
  { name: "Pork", description: "Pork-based dishes" },
  { name: "Seafood", description: "Seafood-based dishes" },
  { name: "Tempeh", description: "Tempeh-based protein" },
  { name: "Tofu", description: "Tofu-based protein" },
  { name: "Turkey", description: "Turkey-based dishes" },
  { name: "Vegan", description: "Plant-based protein" },
  { name: "Vegetarian", description: "Vegetarian protein sources" }
]

proteins.each do |protein_data|
  Protein.find_or_create_by!(name: protein_data[:name]) do |protein|
    protein.description = protein_data[:description]
    protein.active = true
  end
end

# Tags
tags = [
  { name: "bbq", description: "Barbecue and grilled dishes" },
  { name: "breakfast", description: "Breakfast dishes" },
  { name: "brunch", description: "Brunch dishes" },
  { name: "camping", description: "Great for camping trips" },
  { name: "cheap", description: "Budget-friendly meals" },
  { name: "comfort food", description: "Comfort food dishes" },
  { name: "dessert", description: "Sweet desserts" },
  { name: "dinner", description: "Dinner dishes" },
  { name: "easy", description: "Easy to prepare" },
  { name: "healthy", description: "Healthy and nutritious" },
  { name: "holiday", description: "Holiday and special occasion dishes" },
  { name: "lunch", description: "Lunch dishes" },
  { name: "one pot", description: "One pot meals" },
  { name: "party", description: "Great for parties" },
  { name: "quick", description: "Quick to prepare" },
  { name: "snack", description: "Snacks and appetizers" },
  { name: "soup", description: "Soups and stews" },
  { name: "spicy", description: "Spicy dishes" },
  { name: "weekend", description: "Perfect for weekends" }
]

tags.each do |tag_data|
  Tag.find_or_create_by!(name: tag_data[:name]) do |tag|
    tag.description = tag_data[:description]
    tag.active = true
  end
end

# Dietary Requirements
dietary_requirements = [
  { name: "dairy free", description: "Contains no dairy products" },
  { name: "gluten free", description: "Contains no gluten" },
  { name: "halal", description: "Prepared according to Islamic law" },
  { name: "keto", description: "Ketogenic diet friendly" },
  { name: "kosher", description: "Prepared according to Jewish law" },
  { name: "low carb", description: "Low in carbohydrates" },
  { name: "nut free", description: "Contains no nuts" },
  { name: "paleo", description: "Paleolithic diet friendly" },
  { name: "vegan", description: "Contains no animal products" },
  { name: "vegetarian", description: "Contains no meat" }
]

dietary_requirements.each do |req_data|
  DietaryRequirement.find_or_create_by!(name: req_data[:name]) do |req|
    req.description = req_data[:description]
    req.active = true
  end
end

puts "Seeding completed!"
puts "Created #{Cuisine.count} cuisines"
puts "Created #{Protein.count} proteins"
puts "Created #{Tag.count} tags"
puts "Created #{DietaryRequirement.count} dietary requirements"
