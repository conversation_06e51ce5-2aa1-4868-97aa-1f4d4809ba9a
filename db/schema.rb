# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_12_065617) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "cuisines", force: :cascade do |t|
    t.string "name", limit: 50, null: false
    t.text "description"
    t.boolean "active", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_cuisines_on_active"
    t.index ["name"], name: "index_cuisines_on_name", unique: true
  end

  create_table "dietary_requirements", force: :cascade do |t|
    t.string "name", limit: 50, null: false
    t.text "description"
    t.boolean "active", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_dietary_requirements_on_active"
    t.index ["name"], name: "index_dietary_requirements_on_name", unique: true
  end

  create_table "ingredient_substitutions", force: :cascade do |t|
    t.bigint "recipe_ingredient_id", null: false
    t.bigint "substitute_ingredient_id", null: false
    t.bigint "substitute_unit_id", null: false
    t.decimal "substitute_amount", precision: 12, scale: 4, null: false
    t.integer "substitute_preparation"
    t.integer "dietary_requirement", null: false
    t.text "substitute_notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["recipe_ingredient_id", "dietary_requirement"], name: "idx_substitutions_unique", unique: true
    t.index ["recipe_ingredient_id"], name: "index_ingredient_substitutions_on_recipe_ingredient_id"
    t.index ["substitute_ingredient_id"], name: "index_ingredient_substitutions_on_substitute_ingredient_id"
    t.index ["substitute_unit_id"], name: "index_ingredient_substitutions_on_substitute_unit_id"
    t.check_constraint "substitute_amount > 0::numeric", name: "substitute_amount_positive_check"
  end

  create_table "ingredients", force: :cascade do |t|
    t.string "name", limit: 50, null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_ingredients_on_name", unique: true
    t.check_constraint "TRIM(BOTH FROM name) <> ''::text", name: "ingredient_name_not_blank_check"
  end

  create_table "proteins", force: :cascade do |t|
    t.string "name", limit: 50, null: false
    t.text "description"
    t.boolean "active", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_proteins_on_active"
    t.index ["name"], name: "index_proteins_on_name", unique: true
  end

  create_table "ratings", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "recipe_id", null: false
    t.integer "rating", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["recipe_id"], name: "index_ratings_on_recipe_id"
    t.index ["user_id", "recipe_id"], name: "index_ratings_on_user_id_and_recipe_id", unique: true
    t.index ["user_id"], name: "index_ratings_on_user_id"
    t.check_constraint "rating >= 1 AND rating <= 5", name: "rating_range_check"
  end

  create_table "recipe_comments", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "recipe_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["recipe_id"], name: "index_recipe_comments_on_recipe_id"
    t.index ["user_id", "recipe_id"], name: "index_recipe_comments_on_user_id_and_recipe_id", unique: true
    t.index ["user_id"], name: "index_recipe_comments_on_user_id"
  end

  create_table "recipe_dietary_requirements", force: :cascade do |t|
    t.bigint "recipe_id", null: false
    t.bigint "dietary_requirement_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["dietary_requirement_id"], name: "index_recipe_dietary_requirements_on_dietary_requirement_id"
    t.index ["recipe_id", "dietary_requirement_id"], name: "index_recipe_dietary_requirements_unique", unique: true
    t.index ["recipe_id"], name: "index_recipe_dietary_requirements_on_recipe_id"
  end

  create_table "recipe_ingredients", force: :cascade do |t|
    t.bigint "recipe_id", null: false
    t.bigint "ingredient_id", null: false
    t.decimal "amount", precision: 12, scale: 4, null: false
    t.bigint "unit_id", null: false
    t.integer "preparation"
    t.text "notes"
    t.integer "display_order", default: 1, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ingredient_id"], name: "index_recipe_ingredients_on_ingredient_id"
    t.index ["recipe_id", "display_order"], name: "index_recipe_ingredients_on_recipe_id_and_display_order"
    t.index ["recipe_id", "ingredient_id"], name: "index_recipe_ingredients_on_recipe_id_and_ingredient_id", unique: true
    t.index ["recipe_id"], name: "index_recipe_ingredients_on_recipe_id"
    t.index ["unit_id"], name: "index_recipe_ingredients_on_unit_id"
    t.check_constraint "amount > 0::numeric", name: "amount_positive_check"
    t.check_constraint "display_order > 0", name: "display_order_positive_check"
  end

  create_table "recipe_tags", force: :cascade do |t|
    t.bigint "recipe_id", null: false
    t.bigint "tag_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["recipe_id", "tag_id"], name: "index_recipe_tags_on_recipe_id_and_tag_id", unique: true
    t.index ["recipe_id"], name: "index_recipe_tags_on_recipe_id"
    t.index ["tag_id"], name: "index_recipe_tags_on_tag_id"
  end

  create_table "recipe_variants", force: :cascade do |t|
    t.bigint "recipe_id", null: false
    t.integer "dietary_requirement", null: false
    t.string "name", limit: 100
    t.integer "working_minutes", default: 0
    t.integer "total_minutes", default: 0
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["dietary_requirement"], name: "index_recipe_variants_on_dietary_requirement"
    t.index ["recipe_id", "dietary_requirement"], name: "index_recipe_variants_on_recipe_id_and_dietary_requirement", unique: true
    t.index ["recipe_id"], name: "index_recipe_variants_on_recipe_id"
    t.check_constraint "total_minutes >= 0", name: "total_minutes_positive_check"
    t.check_constraint "total_minutes >= working_minutes", name: "total_minutes_gte_working_minutes_check"
    t.check_constraint "working_minutes >= 0", name: "working_minutes_positive_check"
  end

  create_table "recipes", force: :cascade do |t|
    t.string "title", limit: 50, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.decimal "average_rating", precision: 3, scale: 2
    t.integer "rating_count", default: 0, null: false
    t.string "sub_title", limit: 80
    t.integer "cuisine"
    t.integer "protein"
    t.integer "working_minutes", default: 0, null: false
    t.integer "total_minutes", default: 0, null: false
    t.integer "tags", default: [], array: true
    t.integer "dietary_requirements", default: [], array: true
    t.integer "suggested_serves", default: 1, null: false
    t.index ["average_rating"], name: "index_recipes_on_average_rating"
    t.index ["created_at"], name: "index_recipes_on_created_at"
    t.index ["cuisine"], name: "index_recipes_on_cuisine"
    t.index ["dietary_requirements"], name: "index_recipes_on_dietary_requirements", using: :gin
    t.index ["protein"], name: "index_recipes_on_protein"
    t.index ["tags"], name: "index_recipes_on_tags", using: :gin
    t.index ["title"], name: "index_recipes_on_title", unique: true
    t.check_constraint "TRIM(BOTH FROM title) <> ''::text", name: "title_not_blank_check"
    t.check_constraint "array_length(dietary_requirements, 1) > 0 OR dietary_requirements = '{}'::integer[]", name: "dietary_requirements_not_null_array_check"
    t.check_constraint "array_length(tags, 1) > 0 OR tags = '{}'::integer[]", name: "tags_not_null_array_check"
    t.check_constraint "average_rating >= 1.0 AND average_rating <= 5.0", name: "average_rating_range_check"
    t.check_constraint "suggested_serves > 0", name: "suggested_serves_positive_check"
    t.check_constraint "total_minutes >= 0", name: "total_minutes_positive_check"
    t.check_constraint "working_minutes >= 0", name: "working_minutes_positive_check"
  end

  create_table "sessions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_sessions_on_created_at"
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "tags", force: :cascade do |t|
    t.string "name", limit: 50, null: false
    t.text "description"
    t.boolean "active", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_tags_on_active"
    t.index ["name"], name: "index_tags_on_name", unique: true
  end

  create_table "units", force: :cascade do |t|
    t.string "name", limit: 25, null: false
    t.string "abbreviation", limit: 8, null: false
    t.integer "unit_type", null: false
    t.decimal "measure", precision: 12, scale: 4, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["abbreviation"], name: "index_units_on_abbreviation", unique: true
    t.index ["name"], name: "index_units_on_name", unique: true
    t.check_constraint "TRIM(BOTH FROM abbreviation) <> ''::text", name: "unit_abbreviation_not_blank_check"
    t.check_constraint "TRIM(BOTH FROM name) <> ''::text", name: "unit_name_not_blank_check"
    t.check_constraint "measure > 0::numeric", name: "measure_positive_check"
  end

  create_table "users", force: :cascade do |t|
    t.string "email_address", null: false
    t.string "password_digest", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "username", null: false
    t.boolean "admin", default: false, null: false
    t.integer "subscription_level", default: 0, null: false
    t.index ["email_address"], name: "index_users_on_email_address", unique: true
    t.index ["username"], name: "index_users_on_username", unique: true
  end

  create_table "variant_dietary_requirements", force: :cascade do |t|
    t.bigint "recipe_variant_id", null: false
    t.bigint "dietary_requirement_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["dietary_requirement_id"], name: "index_variant_dietary_requirements_on_dietary_requirement_id"
    t.index ["recipe_variant_id", "dietary_requirement_id"], name: "index_variant_dietary_requirements_unique", unique: true
    t.index ["recipe_variant_id"], name: "index_variant_dietary_requirements_on_recipe_variant_id"
  end

  create_table "variant_ingredients", force: :cascade do |t|
    t.bigint "recipe_variant_id", null: false
    t.bigint "ingredient_id", null: false
    t.bigint "unit_id", null: false
    t.decimal "amount", precision: 12, scale: 4, null: false
    t.integer "preparation"
    t.text "notes"
    t.integer "display_order", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ingredient_id"], name: "index_variant_ingredients_on_ingredient_id"
    t.index ["recipe_variant_id", "display_order"], name: "idx_on_recipe_variant_id_display_order_8696c6ee2d"
    t.index ["recipe_variant_id"], name: "index_variant_ingredients_on_recipe_variant_id"
    t.index ["unit_id"], name: "index_variant_ingredients_on_unit_id"
    t.check_constraint "amount > 0::numeric", name: "variant_amount_positive_check"
    t.check_constraint "display_order > 0", name: "variant_display_order_positive_check"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "ingredient_substitutions", "ingredients", column: "substitute_ingredient_id"
  add_foreign_key "ingredient_substitutions", "recipe_ingredients"
  add_foreign_key "ingredient_substitutions", "units", column: "substitute_unit_id"
  add_foreign_key "ratings", "recipes"
  add_foreign_key "ratings", "users"
  add_foreign_key "recipe_comments", "recipes"
  add_foreign_key "recipe_comments", "users", on_delete: :cascade
  add_foreign_key "recipe_dietary_requirements", "dietary_requirements"
  add_foreign_key "recipe_dietary_requirements", "recipes"
  add_foreign_key "recipe_ingredients", "ingredients"
  add_foreign_key "recipe_ingredients", "recipes"
  add_foreign_key "recipe_ingredients", "units"
  add_foreign_key "recipe_tags", "recipes"
  add_foreign_key "recipe_tags", "tags"
  add_foreign_key "recipe_variants", "recipes"
  add_foreign_key "sessions", "users"
  add_foreign_key "variant_dietary_requirements", "dietary_requirements"
  add_foreign_key "variant_dietary_requirements", "recipe_variants"
  add_foreign_key "variant_ingredients", "ingredients"
  add_foreign_key "variant_ingredients", "recipe_variants"
  add_foreign_key "variant_ingredients", "units"
end
