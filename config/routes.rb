Rails.application.routes.draw do
  namespace :admin do
    get "", to: "admin#index", as: "root"
    get "enums/edit/:type/:id", to: "admin#edit_enum", as: "edit_enum"
    patch "enums/update/:type/:id", to: "admin#update_enum", as: "update_enum"
    post "enums/toggle_active/:type/:id", to: "admin#toggle_active", as: "toggle_enum"
    post "enums/create/:type", to: "admin#create", as: "create_enum"
    delete "enums/destroy/:type/:id", to: "admin#destroy", as: "destroy_enum"

    resources :ingredients, except: [ :show ]
    resources :units, except: [ :show ]
  end
  resource :session
  resources :passwords, param: :token
  resource :user, only: [ :edit, :update ]
  root "recipes#index"

  # Main resources
  resources :ingredients
  resources :units

  # Nested resources under recipes
  resources :recipes do
    resources :recipe_ingredients, except: [ :index, :show ]
    resources :recipe_variants, except: [ :index, :show ] do
      resources :variant_ingredients, except: [ :index, :show ]
    end
    resources :ratings, only: [ :create, :update, :destroy ]
    resources :recipe_comments, only: [ :create, :edit, :update, :destroy ]
  end

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Defines the root path route ("/")
  # root "posts#index"
end
